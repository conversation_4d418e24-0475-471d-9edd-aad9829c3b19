"""
K线图表 ViewModel
负责K线图表的数据管理和业务逻辑
"""

from __future__ import annotations
from typing import Any, Optional, Dict, List, Tuple
import logging
from datetime import datetime, timedelta
from decimal import Decimal

from PySide6.QtCore import QObject, Signal, QTimer
from PySide6.QtWidgets import QApplication

from core.event_types import BarEvent, TickEvent
from core.data_types import BarData, TickData, Interval
from data_service.storage import DataStorage


class ChartViewModel(QObject):
    """
    K线图表ViewModel
    
    职责:
    1. 订阅实时数据事件
    2. 管理历史数据加载
    3. 处理图表数据格式转换
    4. 提供图表更新信号
    """
    
    # 信号定义
    bars_updated = Signal(str, list)  # symbol, bars_data
    tick_updated = Signal(str, object)  # symbol, tick_data
    chart_error = Signal(str)  # error_message
    
    def __init__(self, event_bus: Any, config: Dict[str, Any]):
        super().__init__()
        self.event_bus = event_bus
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 数据存储
        data_config = config.get('data_service', {})
        storage_path = data_config.get('storage_path', './data')
        storage_type = data_config.get('storage_type', 'feather')
        self.data_storage = DataStorage(storage_path, storage_type)
        
        # 当前显示的品种和周期
        self.current_symbol: Optional[str] = None
        self.current_interval: Interval = Interval.MINUTE_1
        
        # 数据缓存
        self.bars_cache: Dict[str, List[BarData]] = {}
        self.latest_ticks: Dict[str, TickData] = {}
        
        # 图表配置
        self.max_bars_display = 1000  # 最大显示K线数量
        self.auto_scroll = True  # 自动滚动
        
        # 定时器用于批量更新
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._batch_update)
        self.update_timer.setSingleShot(True)
        
        # 订阅事件
        self._subscribe_events()
        
    def _subscribe_events(self) -> None:
        """订阅事件"""
        if self.event_bus:
            self.event_bus.subscribe("bar", self._on_bar_event, "chart_vm_bar")
            self.event_bus.subscribe("tick", self._on_tick_event, "chart_vm_tick")
    
    def set_symbol(self, symbol: str, interval: Interval = Interval.MINUTE_1) -> None:
        """
        设置当前显示的品种和周期
        
        Args:
            symbol: 品种代码
            interval: 时间周期
        """
        try:
            self.current_symbol = symbol
            self.current_interval = interval
            
            # 加载历史数据
            self._load_historical_data(symbol, interval)
            
            self.logger.info(f"切换图表品种: {symbol}, 周期: {interval.value}")
            
        except Exception as e:
            self.logger.error(f"设置品种失败: {e}")
            self.chart_error.emit(f"设置品种失败: {e}")
    
    def _load_historical_data(self, symbol: str, interval: Interval) -> None:
        """加载历史数据"""
        try:
            # 计算时间范围（最近30天）
            end_time = datetime.now()
            start_time = end_time - timedelta(days=30)
            
            # 从存储中加载数据
            bars = self.data_storage.get_bars(
                symbol=symbol,
                interval=interval,
                start_time=start_time,
                end_time=end_time
            )
            
            if bars:
                # 限制显示数量
                if len(bars) > self.max_bars_display:
                    bars = bars[-self.max_bars_display:]
                
                # 缓存数据
                cache_key = f"{symbol}_{interval.value}"
                self.bars_cache[cache_key] = bars
                
                # 转换为图表数据格式
                chart_data = self._convert_bars_to_chart_data(bars)
                
                # 发射更新信号
                self.bars_updated.emit(symbol, chart_data)
                
                self.logger.info(f"加载历史数据: {symbol}, {len(bars)}条K线")
            else:
                self.logger.warning(f"未找到历史数据: {symbol}")
                
        except Exception as e:
            self.logger.error(f"加载历史数据失败: {e}")
            self.chart_error.emit(f"加载历史数据失败: {e}")
    
    def _on_bar_event(self, event: BarEvent) -> None:
        """处理K线事件"""
        try:
            bar = event.data
            
            # 只处理当前显示品种的数据
            if (self.current_symbol and 
                bar.symbol == self.current_symbol and 
                bar.interval == self.current_interval):
                
                # 更新缓存
                cache_key = f"{bar.symbol}_{bar.interval.value}"
                if cache_key not in self.bars_cache:
                    self.bars_cache[cache_key] = []
                
                bars = self.bars_cache[cache_key]
                
                # 检查是否是更新最后一根K线还是新增K线
                if bars and bars[-1].datetime == bar.datetime:
                    # 更新最后一根K线
                    bars[-1] = bar
                else:
                    # 新增K线
                    bars.append(bar)
                    
                    # 限制数量
                    if len(bars) > self.max_bars_display:
                        bars.pop(0)
                
                # 延迟批量更新（避免频繁更新UI）
                if not self.update_timer.isActive():
                    self.update_timer.start(500)  # 500ms延迟，降低更新频率
                    
        except Exception as e:
            self.logger.error(f"处理K线事件失败: {e}")
    
    def _on_tick_event(self, event: TickEvent) -> None:
        """处理Tick事件"""
        try:
            tick = event.data
            
            # 缓存最新Tick
            self.latest_ticks[tick.symbol] = tick
            
            # 只处理当前显示品种
            if self.current_symbol and tick.symbol == self.current_symbol:
                self.tick_updated.emit(tick.symbol, tick)
                
        except Exception as e:
            self.logger.error(f"处理Tick事件失败: {e}")
    
    def _batch_update(self) -> None:
        """批量更新图表"""
        try:
            if not self.current_symbol:
                return
                
            cache_key = f"{self.current_symbol}_{self.current_interval.value}"
            if cache_key in self.bars_cache:
                bars = self.bars_cache[cache_key]
                chart_data = self._convert_bars_to_chart_data(bars)
                self.bars_updated.emit(self.current_symbol, chart_data)
                
        except Exception as e:
            self.logger.error(f"批量更新失败: {e}")
    
    def _convert_bars_to_chart_data(self, bars: List[BarData]) -> List[Tuple]:
        """
        将BarData转换为图表数据格式
        
        Returns:
            List[Tuple]: [(timestamp, open, high, low, close, volume), ...]
        """
        chart_data = []
        
        for bar in bars:
            timestamp = bar.datetime.timestamp()
            open_price = float(bar.open_price)
            high_price = float(bar.high_price)
            low_price = float(bar.low_price)
            close_price = float(bar.close_price)
            volume = bar.volume
            
            chart_data.append((timestamp, open_price, high_price, low_price, close_price, volume))
        
        return chart_data
    
    def get_latest_price(self, symbol: str) -> Optional[Decimal]:
        """获取最新价格"""
        if symbol in self.latest_ticks:
            return self.latest_ticks[symbol].last_price
        return None
    
    def refresh_data(self) -> None:
        """刷新数据"""
        if self.current_symbol:
            self._load_historical_data(self.current_symbol, self.current_interval)
