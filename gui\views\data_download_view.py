#!/usr/bin/env python3
"""
数据下载视图
提供数据下载功能的GUI界面
"""

from __future__ import annotations
from typing import Any, Optional, List
import logging
from datetime import datetime, date, timedelta

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QLineEdit, QPushButton, QComboBox, QDateEdit,
    QProgressBar, QTextEdit, QGroupBox, QCheckBox, QSpinBox,
    QMessageBox, QFrame, QSplitter, QWidget
)
from PySide6.QtCore import Qt, Signal, QThread, QDate, QTimer
from PySide6.QtGui import QFont

from core.data_types import Interval


class DataDownloadWorker(QThread):
    """
    数据下载工作线程
    """
    # 信号定义
    progress_updated = Signal(int)  # 进度更新
    status_updated = Signal(str)    # 状态更新
    log_updated = Signal(str)       # 日志更新
    download_completed = Signal(bool, str)  # 下载完成(成功, 消息)
    
    def __init__(self, symbol: str, start_date: date, end_date: date, 
                 interval: str, source: str = "binance"):
        super().__init__()
        self.symbol = symbol
        self.start_date = start_date
        self.end_date = end_date
        self.interval = interval
        self.source = source
        self.is_cancelled = False
        
    def cancel(self):
        """取消下载"""
        self.is_cancelled = True
        
    def run(self):
        """执行下载任务"""
        try:
            self.status_updated.emit("正在初始化...")
            self.log_updated.emit(f"开始下载 {self.symbol} 数据")
            self.log_updated.emit(f"时间范围: {self.start_date} 到 {self.end_date}")
            self.log_updated.emit(f"时间周期: {self.interval}")
            self.log_updated.emit(f"数据源: {self.source}")
            
            # 导入必要模块
            import sys
            from pathlib import Path
            project_root = Path(__file__).parent.parent.parent
            sys.path.insert(0, str(project_root))
            
            from data_service.connectors.binance_connector import BinanceConnector
            from data_service.storage import DataStorage
            from core.data_types import Interval
            
            # 初始化连接器和存储
            self.status_updated.emit("连接到数据源...")
            connector = BinanceConnector()
            connector.connect()
            
            storage_path = project_root / "data" / "bars_yearly"
            storage = DataStorage(str(storage_path), storage_type="parquet")
            
            # 计算总天数用于进度显示
            total_days = (self.end_date - self.start_date).days + 1
            current_day = 0
            
            # 按年分批下载
            current_date = self.start_date
            all_bars = []
            
            while current_date <= self.end_date and not self.is_cancelled:
                # 计算当前年的结束日期
                year_end = date(current_date.year, 12, 31)
                batch_end = min(year_end, self.end_date)
                
                self.status_updated.emit(f"下载 {current_date.year} 年数据...")
                self.log_updated.emit(f"下载时间段: {current_date} 到 {batch_end}")
                
                try:
                    # 获取历史数据
                    bars = connector.get_historical_bars(
                        symbol=self.symbol,
                        interval=Interval(self.interval),
                        start_time=datetime.combine(current_date, datetime.min.time()),
                        end_time=datetime.combine(batch_end, datetime.max.time())
                    )
                    
                    if bars:
                        all_bars.extend(bars)
                        self.log_updated.emit(f"获取到 {len(bars)} 条数据")
                        
                        # 按年保存数据
                        year_bars = [bar for bar in bars if bar.datetime.year == current_date.year]
                        if year_bars:
                            # 保存年度数据
                            self._save_yearly_data(storage, year_bars, current_date.year)
                            
                    else:
                        self.log_updated.emit(f"未获取到数据: {current_date} 到 {batch_end}")
                        
                except Exception as e:
                    self.log_updated.emit(f"下载失败: {str(e)}")
                    
                # 更新进度
                days_processed = (batch_end - self.start_date).days + 1
                progress = int((days_processed / total_days) * 100)
                self.progress_updated.emit(progress)
                
                # 移动到下一年
                current_date = date(current_date.year + 1, 1, 1)
                
                # 短暂延迟避免API限制
                self.msleep(100)
            
            # 清理资源
            connector.disconnect()
            storage.close()
            
            if self.is_cancelled:
                self.status_updated.emit("下载已取消")
                self.download_completed.emit(False, "用户取消下载")
            else:
                self.status_updated.emit("下载完成")
                self.log_updated.emit(f"总共下载 {len(all_bars)} 条数据")
                self.download_completed.emit(True, f"成功下载 {len(all_bars)} 条数据")
                
        except Exception as e:
            error_msg = f"下载失败: {str(e)}"
            self.status_updated.emit(error_msg)
            self.log_updated.emit(error_msg)
            self.download_completed.emit(False, error_msg)
    
    def _save_yearly_data(self, storage, bars, year):
        """保存年度数据"""
        try:
            # 按年保存数据的逻辑
            import pandas as pd
            from pathlib import Path
            
            # 转换为DataFrame
            data = []
            for bar in bars:
                data.append({
                    'datetime': bar.datetime,
                    'open': float(bar.open_price),
                    'high': float(bar.high_price),
                    'low': float(bar.low_price),
                    'close': float(bar.close_price),
                    'volume': bar.volume,
                    'turnover': bar.turnover,
                    'open_interest': bar.open_interest
                })
            
            if data:
                df = pd.DataFrame(data)
                df = df.sort_values('datetime').reset_index(drop=True)
                
                # 保存为年度文件
                filename = f"{self.symbol}_{self.interval}_{year}.parquet"
                filepath = Path(storage.base_path) / filename
                df.to_parquet(filepath, index=False)
                
                self.log_updated.emit(f"保存年度文件: {filename}, {len(df)} 条记录")
                
        except Exception as e:
            self.log_updated.emit(f"保存数据失败: {str(e)}")


class DataDownloadDialog(QDialog):
    """
    数据下载对话框
    """
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.worker = None
        self._init_ui()
        self._connect_signals()
        
    def _init_ui(self):
        """初始化UI"""
        self.setWindowTitle("数据下载工具")
        self.setGeometry(200, 200, 800, 600)
        self.setModal(True)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧：参数设置
        left_widget = self._create_parameter_panel()
        splitter.addWidget(left_widget)
        
        # 右侧：日志和进度
        right_widget = self._create_progress_panel()
        splitter.addWidget(right_widget)
        
        # 设置分割器比例
        splitter.setSizes([350, 450])
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.download_btn = QPushButton("开始下载")
        self.download_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        self.download_btn.clicked.connect(self._start_download)
        
        self.cancel_btn = QPushButton("取消下载")
        self.cancel_btn.setStyleSheet("background-color: #f44336; color: white;")
        self.cancel_btn.setEnabled(False)
        self.cancel_btn.clicked.connect(self._cancel_download)
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close)
        
        button_layout.addWidget(self.download_btn)
        button_layout.addWidget(self.cancel_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)
        
        main_layout.addLayout(button_layout)
        
    def _create_parameter_panel(self) -> QWidget:
        """创建参数设置面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 数据源选择
        source_group = QGroupBox("数据源")
        source_layout = QVBoxLayout(source_group)
        
        self.source_combo = QComboBox()
        self.source_combo.addItems(["Binance源"])
        source_layout.addWidget(self.source_combo)
        
        layout.addWidget(source_group)
        
        # 下载参数
        param_group = QGroupBox("下载参数")
        param_layout = QFormLayout(param_group)
        
        # 品种
        self.symbol_edit = QLineEdit()
        self.symbol_edit.setText("ETHUSDT")
        self.symbol_edit.setPlaceholderText("输入交易品种，如ETHUSDT")
        param_layout.addRow("交易品种:", self.symbol_edit)
        
        # 时间周期
        self.interval_combo = QComboBox()
        intervals = ["1m", "3m", "5m", "15m", "30m", "1h", "2h", "4h", "6h", "8h", "12h", "1d", "3d", "1w", "1M"]
        self.interval_combo.addItems(intervals)
        self.interval_combo.setCurrentText("15m")
        param_layout.addRow("时间周期:", self.interval_combo)
        
        # 开始日期
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addYears(-5))
        self.start_date_edit.setCalendarPopup(True)
        param_layout.addRow("开始日期:", self.start_date_edit)
        
        # 结束日期
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setCalendarPopup(True)
        param_layout.addRow("结束日期:", self.end_date_edit)
        
        layout.addWidget(param_group)
        
        # 高级选项
        advanced_group = QGroupBox("高级选项")
        advanced_layout = QVBoxLayout(advanced_group)
        
        self.merge_yearly_check = QCheckBox("按年合并保存")
        self.merge_yearly_check.setChecked(True)
        self.merge_yearly_check.setToolTip("将数据按年份合并保存，减少文件数量")
        advanced_layout.addWidget(self.merge_yearly_check)
        
        self.overwrite_check = QCheckBox("覆盖已存在文件")
        self.overwrite_check.setToolTip("如果目标文件已存在，是否覆盖")
        advanced_layout.addWidget(self.overwrite_check)
        
        layout.addWidget(advanced_group)
        
        layout.addStretch()
        
        return widget
        
    def _create_progress_panel(self) -> QWidget:
        """创建进度和日志面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 进度信息
        progress_group = QGroupBox("下载进度")
        progress_layout = QVBoxLayout(progress_group)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setFont(QFont("Arial", 10, QFont.Bold))
        progress_layout.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        progress_layout.addWidget(self.progress_bar)
        
        layout.addWidget(progress_group)
        
        # 日志输出
        log_group = QGroupBox("下载日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        log_layout.addWidget(self.log_text)
        
        # 日志控制
        log_control_layout = QHBoxLayout()
        
        self.clear_log_btn = QPushButton("清空日志")
        self.clear_log_btn.clicked.connect(self.log_text.clear)
        
        self.save_log_btn = QPushButton("保存日志")
        self.save_log_btn.clicked.connect(self._save_log)
        
        log_control_layout.addWidget(self.clear_log_btn)
        log_control_layout.addWidget(self.save_log_btn)
        log_control_layout.addStretch()
        
        log_layout.addLayout(log_control_layout)
        
        layout.addWidget(log_group)
        
        return widget
        
    def _connect_signals(self):
        """连接信号"""
        # 日期验证
        self.start_date_edit.dateChanged.connect(self._validate_dates)
        self.end_date_edit.dateChanged.connect(self._validate_dates)
        
    def _validate_dates(self):
        """验证日期设置"""
        start_date = self.start_date_edit.date().toPython()
        end_date = self.end_date_edit.date().toPython()
        
        if start_date >= end_date:
            self.download_btn.setEnabled(False)
            self.status_label.setText("错误: 开始日期必须早于结束日期")
            self.status_label.setStyleSheet("color: red;")
        else:
            self.download_btn.setEnabled(True)
            self.status_label.setText("就绪")
            self.status_label.setStyleSheet("color: black;")
            
    def _start_download(self):
        """开始下载"""
        # 验证输入
        symbol = self.symbol_edit.text().strip().upper()
        if not symbol:
            QMessageBox.warning(self, "输入错误", "请输入交易品种")
            return
            
        start_date = self.start_date_edit.date().toPython()
        end_date = self.end_date_edit.date().toPython()
        interval = self.interval_combo.currentText()
        source = "binance"  # 目前只支持Binance
        
        # 确认下载
        days = (end_date - start_date).days + 1
        msg = f"确认下载以下数据？\n\n"
        msg += f"交易品种: {symbol}\n"
        msg += f"时间周期: {interval}\n"
        msg += f"时间范围: {start_date} 到 {end_date} ({days} 天)\n"
        msg += f"数据源: {self.source_combo.currentText()}"
        
        reply = QMessageBox.question(self, "确认下载", msg, 
                                   QMessageBox.Yes | QMessageBox.No)
        if reply != QMessageBox.Yes:
            return
            
        # 开始下载
        self.download_btn.setEnabled(False)
        self.cancel_btn.setEnabled(True)
        self.close_btn.setEnabled(False)
        
        # 清空日志和进度
        self.log_text.clear()
        self.progress_bar.setValue(0)
        
        # 创建工作线程
        self.worker = DataDownloadWorker(symbol, start_date, end_date, interval, source)
        self.worker.progress_updated.connect(self.progress_bar.setValue)
        self.worker.status_updated.connect(self.status_label.setText)
        self.worker.log_updated.connect(self._append_log)
        self.worker.download_completed.connect(self._on_download_completed)
        
        # 启动下载
        self.worker.start()
        
    def _cancel_download(self):
        """取消下载"""
        if self.worker and self.worker.isRunning():
            self.worker.cancel()
            self.worker.wait(3000)  # 等待3秒
            
        self._reset_ui()
        
    def _on_download_completed(self, success: bool, message: str):
        """下载完成处理"""
        self._reset_ui()
        
        if success:
            self.progress_bar.setValue(100)
            self.status_label.setText("下载完成")
            self.status_label.setStyleSheet("color: green;")
            QMessageBox.information(self, "下载完成", message)
        else:
            self.status_label.setText("下载失败")
            self.status_label.setStyleSheet("color: red;")
            QMessageBox.warning(self, "下载失败", message)
            
    def _reset_ui(self):
        """重置UI状态"""
        self.download_btn.setEnabled(True)
        self.cancel_btn.setEnabled(False)
        self.close_btn.setEnabled(True)
        
    def _append_log(self, message: str):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_line = f"[{timestamp}] {message}"
        self.log_text.append(log_line)
        
        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.End)
        self.log_text.setTextCursor(cursor)
        
    def _save_log(self):
        """保存日志"""
        from PySide6.QtWidgets import QFileDialog
        
        filename, _ = QFileDialog.getSaveFileName(
            self, "保存日志", f"download_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "文本文件 (*.txt);;所有文件 (*.*)"
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())
                QMessageBox.information(self, "保存成功", f"日志已保存到: {filename}")
            except Exception as e:
                QMessageBox.warning(self, "保存失败", f"保存日志失败: {str(e)}")
                
    def closeEvent(self, event):
        """关闭事件处理"""
        if self.worker and self.worker.isRunning():
            reply = QMessageBox.question(self, "确认关闭", 
                                       "下载正在进行中，确认关闭窗口？\n这将取消当前下载。",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.Yes:
                self._cancel_download()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()