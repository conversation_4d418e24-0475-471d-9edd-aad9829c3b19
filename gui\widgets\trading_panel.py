#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
右侧交易面板组件
可折叠的交易面板实现
"""

from __future__ import annotations
from typing import Any, Optional
import logging

try:
    from PySide6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QSplitter
    )
    from PySide6.QtCore import Qt, Signal, QPropertyAnimation, QEasingCurve
    from PySide6.QtGui import QIcon
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False


class TradingPanel(QWidget):
    """
    右侧交易面板组件
    真正的侧边栏实现，可以完全隐藏
    """
    
    # 信号定义
    panel_toggled = Signal(bool)  # 面板切换状态
    
    def __init__(self, trading_view: QWidget, portfolio_view: QWidget, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        
        # 保存视图引用
        self.trading_view = trading_view
        self.portfolio_view = portfolio_view
        
        # 状态变量
        self.is_visible = True
        self.panel_width = 400
        
        self._setup_ui()
    
    def _setup_ui(self) -> None:
        """设置UI界面"""
        # 设置固定宽度
        self.setFixedWidth(self.panel_width)
        
        # 主布局（垂直）
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建顶部控制栏
        self.control_bar = QWidget()
        self.control_bar.setFixedHeight(40)
        self.control_bar.setStyleSheet("""
            QWidget {
                background-color: #34495e;
                border-bottom: 1px solid #2c3e50;
            }
        """)
        
        # 控制栏布局
        control_layout = QHBoxLayout(self.control_bar)
        control_layout.setContentsMargins(10, 5, 10, 5)
        
        # 标题标签
        title_label = QPushButton("交易面板")
        title_label.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #ecf0f1;
                font-size: 14px;
                font-weight: bold;
                border: none;
                text-align: left;
                padding: 5px;
            }
        """)
        title_label.setEnabled(False)
        
        # 折叠按钮
        self.toggle_button = QPushButton("▼")
        self.toggle_button.setFixedSize(30, 30)
        self.toggle_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                font-size: 12px;
                font-weight: bold;
                border-radius: 15px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1f5f8b;
            }
        """)
        self.toggle_button.setToolTip("折叠交易面板")
        self.toggle_button.clicked.connect(self.toggle_panel)
        
        # 添加到控制栏
        control_layout.addWidget(title_label)
        control_layout.addStretch()
        control_layout.addWidget(self.toggle_button)
        
        # 创建内容容器
        self.content_container = QWidget()
        content_layout = QVBoxLayout(self.content_container)
        content_layout.setContentsMargins(5, 5, 5, 5)
        content_layout.setSpacing(5)
        
        # 添加交易视图和投资组合视图
        content_layout.addWidget(self.trading_view)
        content_layout.addWidget(self.portfolio_view)
        
        # 添加到主布局
        main_layout.addWidget(self.control_bar)
        main_layout.addWidget(self.content_container)
    
    def toggle_panel(self) -> None:
        """切换面板显示/隐藏"""
        try:
            if self.is_visible:
                self._hide_panel()
            else:
                self._show_panel()
            
            # 发射信号
            self.panel_toggled.emit(self.is_visible)
            
        except Exception as e:
            self.logger.error(f"切换交易面板失败: {e}")
    
    def _hide_panel(self) -> None:
        """隐藏面板内容"""
        self.content_container.hide()
        self.toggle_button.setText("▲")
        self.toggle_button.setToolTip("展开交易面板")
        self.is_visible = False
        # 调整面板高度，只保留控制栏
        self.setFixedHeight(40)
        self.logger.info("交易面板已折叠")
    
    def _show_panel(self) -> None:
        """显示面板内容"""
        self.content_container.show()
        self.toggle_button.setText("▼")
        self.toggle_button.setToolTip("折叠交易面板")
        self.is_visible = True
        # 恢复面板高度
        self.setMaximumHeight(16777215)
        self.setMinimumHeight(0)
        self.logger.info("交易面板已展开")
    
    def set_panel_width(self, width: int) -> None:
        """设置面板宽度"""
        self.panel_width = width
        self.setFixedWidth(width)
    
    def get_panel_width(self) -> int:
        """获取面板宽度"""
        return self.panel_width
    
    def is_panel_visible(self) -> bool:
        """检查面板是否可见"""
        return self.is_visible
    
    def show_panel(self) -> None:
        """外部调用显示面板"""
        if not self.is_visible:
            self._show_panel()
            self.panel_toggled.emit(self.is_visible)
    
    def hide_panel(self) -> None:
        """外部调用隐藏面板"""
        if self.is_visible:
            self._hide_panel()
            self.panel_toggled.emit(self.is_visible)
    
    def get_trading_view(self) -> QWidget:
        """获取交易视图"""
        return self.trading_view
    
    def get_portfolio_view(self) -> QWidget:
        """获取投资组合视图"""
        return self.portfolio_view
    
    def set_trading_view(self, view: QWidget) -> None:
        """设置交易视图"""
        if self.trading_view:
            # 移除旧视图
            layout = self.content_container.layout()
            layout.removeWidget(self.trading_view)
            self.trading_view.setParent(None)
        
        # 添加新视图
        self.trading_view = view
        layout = self.content_container.layout()
        layout.insertWidget(0, self.trading_view)
    
    def set_portfolio_view(self, view: QWidget) -> None:
        """设置投资组合视图"""
        if self.portfolio_view:
            # 移除旧视图
            layout = self.content_container.layout()
            layout.removeWidget(self.portfolio_view)
            self.portfolio_view.setParent(None)
        
        # 添加新视图
        self.portfolio_view = view
        layout = self.content_container.layout()
        layout.addWidget(self.portfolio_view)


class CollapsibleTradingPanel(TradingPanel):
    """
    可折叠的交易面板
    继承自TradingPanel，保持兼容性
    """
    
    def __init__(self, trading_view: QWidget, portfolio_view: QWidget, parent: Optional[QWidget] = None):
        super().__init__(trading_view, portfolio_view, parent)
        self.logger.info("CollapsibleTradingPanel初始化完成")