#!/usr/bin/env python3
"""
获取ETHUSDT 15分钟K线数据脚本
使用Binance API获取最近5年的ETHUSDT 15分钟K线数据并保存为Parquet格式
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from data_service.connectors.binance_connector import BinanceConnector
    from data_service.storage import DataStorage
    from core.data_types import Interval
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    try:
        # 检查python-binance是否安装
        try:
            import binance
            logger.info("python-binance库已安装")
        except ImportError:
            logger.error("python-binance库未安装，请运行: pip install python-binance")
            return False
        
        # 配置参数
        symbol = "ETHUSDT"
        interval = "15m"
        
        # 计算时间范围（最近5年）
        end_time = datetime.now()
        start_time = end_time - timedelta(days=5*365)  # 5年
        
        logger.info(f"开始获取 {symbol} {interval} 数据")
        logger.info(f"时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} 到 {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 创建Binance连接器
        binance_config = {
            'testnet': False,  # 使用正式环境
            'api_key': '',     # 公共API不需要密钥
            'api_secret': ''
        }
        
        connector = BinanceConnector("binance", binance_config)
        
        # 连接到Binance
        if not connector.connect():
            logger.error("连接Binance失败")
            return False
        
        logger.info("成功连接到Binance API")
        
        # 创建数据存储器（使用Parquet格式）
        storage_path = project_root / "data" / "bars"
        storage = DataStorage(str(storage_path), storage_type="parquet")
        
        logger.info(f"数据将保存到: {storage_path}")
        
        # 分批获取数据（Binance API有限制，每次最多1000条）
        all_bars = []
        current_start = start_time
        batch_size = timedelta(days=30)  # 每批30天的数据
        
        while current_start < end_time:
            current_end = min(current_start + batch_size, end_time)
            
            logger.info(f"获取数据批次: {current_start.strftime('%Y-%m-%d')} 到 {current_end.strftime('%Y-%m-%d')}")
            
            # 获取历史K线数据
            bars = connector.get_historical_bars(
                symbol=symbol,
                start_time=current_start,
                end_time=current_end,
                interval=interval
            )
            
            if bars:
                all_bars.extend(bars)
                logger.info(f"获取到 {len(bars)} 条K线数据")
            else:
                logger.warning(f"未获取到数据: {current_start} - {current_end}")
            
            current_start = current_end
        
        if not all_bars:
            logger.error("未获取到任何数据")
            return False
        
        logger.info(f"总共获取到 {len(all_bars)} 条K线数据")
        
        # 保存数据为Parquet格式
        logger.info("开始保存数据...")
        success = storage.save_bar_data(all_bars, async_write=False)
        
        if success:
            logger.info("数据保存成功！")
            
            # 显示数据统计信息
            if all_bars:
                first_bar = all_bars[0]
                last_bar = all_bars[-1]
                logger.info(f"数据时间范围: {first_bar.datetime} 到 {last_bar.datetime}")
                logger.info(f"数据文件保存在: {storage_path}")
        else:
            logger.error("数据保存失败")
            return False
        
        # 断开连接
        connector.disconnect()
        storage.close()
        
        logger.info("数据获取和保存完成！")
        return True
        
    except Exception as e:
        logger.error(f"执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ ETHUSDT 15分钟K线数据获取成功！")
        print("数据已保存为Parquet格式")
    else:
        print("\n❌ 数据获取失败，请检查日志信息")
        sys.exit(1)