#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
侧边栏功能测试脚本
测试自选品种和搜索功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from PySide6.QtCore import Qt
from gui.widgets.sidebar import Sidebar, SidebarPopup

class TestMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("侧边栏功能测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 创建测试按钮
        test_btn = QPushButton("点击测试侧边栏功能")
        test_btn.clicked.connect(self.show_test_info)
        layout.addWidget(test_btn)
        
        # 创建侧边栏
        self.sidebar = Sidebar(self)
        self.sidebar.setGeometry(0, 0, 60, self.height())
        
        # 创建弹窗
        self.popup = SidebarPopup(self)
        
        # 连接信号
        self.sidebar.popup_show_requested.connect(self.show_popup)
        self.sidebar.popup_hide_requested.connect(self.hide_popup)
        self.popup.close_requested.connect(self.hide_popup)
        
    def show_popup(self, index: int):
        """显示弹窗"""
        self.popup.show_module(index)
        # 设置弹窗位置
        popup_x = 70
        popup_y = 50
        self.popup.setGeometry(popup_x, popup_y, 400, 600)
        self.popup.show()
        
    def hide_popup(self):
        """隐藏弹窗"""
        self.popup.hide()
        
    def show_test_info(self):
        """显示测试信息"""
        print("\n=== 侧边栏功能测试指南 ===")
        print("1. 点击左侧侧边栏的'币圈'按钮")
        print("2. 在弹出的窗口中测试以下功能:")
        print("   - 点击'自选'按钮，应该只显示 SOLUSDT, ETHUSDT, BTCUSDT")
        print("   - 点击'全部'按钮，应该显示所有币种")
        print("   - 在搜索框中输入'ETH'，应该能找到ETHUSDT")
        print("   - 在搜索框中输入'BTC'，应该能找到BTCUSDT")
        print("   - 在搜索框中输入'SOL'，应该能找到SOLUSDT")
        print("3. 测试其他模块的搜索功能")
        print("\n如果功能正常，说明修改成功！")
        
    def resizeEvent(self, event):
        """窗口大小改变时调整侧边栏"""
        super().resizeEvent(event)
        if hasattr(self, 'sidebar'):
            self.sidebar.setGeometry(0, 0, 60, self.height())

def main():
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyleSheet("""
        QMainWindow {
            background-color: #2c3e50;
        }
        QPushButton {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #2980b9;
        }
    """)
    
    window = TestMainWindow()
    window.show()
    
    # 显示测试指南
    window.show_test_info()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()