#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试侧边栏币圈模块的搜索功能
验证虚拟币列表数据在侧边栏悬浮窗中的显示和搜索功能
"""

import sys
import os
import logging
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

try:
    from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QHBoxLayout
    from PySide6.QtCore import Qt, QTimer
    GUI_AVAILABLE = True
except ImportError:
    print("PySide6 不可用，无法运行GUI测试")
    GUI_AVAILABLE = False
    sys.exit(1)

try:
    from gui.widgets.sidebar import Sidebar, SidebarPopup
    from gui.viewmodels.crypto_list_vm import CryptoListViewModel
    from core.event_bus import EventBus
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)


class TestSidebarCryptoWindow(QMainWindow):
    """测试侧边栏币圈搜索功能的窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("侧边栏币圈搜索功能测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # 配置
        self.config = {
            'data_service': {
                'connectors': {
                    'binance': {
                        'enabled': True,
                        'api_key': '',
                        'api_secret': '',
                        'testnet': False,
                        'base_url': ''
                    }
                }
            }
        }
        
        self._setup_ui()
        
        # 延迟初始化，确保UI完全加载
        QTimer.singleShot(1000, self._init_crypto_data)
        
    def _setup_ui(self) -> None:
        """设置UI界面"""
        # 中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建侧边栏
        self.sidebar = Sidebar()
        main_layout.addWidget(self.sidebar)
        
        # 创建侧边栏弹窗
        self.sidebar_popup = SidebarPopup(self)
        self.sidebar.set_popup_widget(self.sidebar_popup)
        
        # 主内容区域
        content_widget = QWidget()
        content_widget.setStyleSheet("""
            QWidget {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
            }
        """)
        content_layout = QVBoxLayout(content_widget)
        
        # 添加说明文本
        from PySide6.QtWidgets import QLabel
        info_label = QLabel("""
        <h2>侧边栏币圈搜索功能测试</h2>
        <p><b>测试步骤：</b></p>
        <ol>
            <li>点击左侧边栏的"币圈"按钮</li>
            <li>等待虚拟币数据加载完成</li>
            <li>在弹窗的搜索框中输入币种名称（如：BTC、ETH、BNB等）</li>
            <li>观察表格数据是否根据搜索条件进行过滤</li>
            <li>清空搜索框，观察是否显示所有数据</li>
        </ol>
        <p><b>预期结果：</b></p>
        <ul>
            <li>搜索功能能够实时过滤虚拟币数据</li>
            <li>支持按品种名称、基础资产、报价资产搜索</li>
            <li>搜索不区分大小写</li>
            <li>数据显示格式与虚拟币列表视图一致</li>
        </ul>
        """)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            QLabel {
                padding: 20px;
                background-color: white;
                border-radius: 10px;
                font-size: 14px;
                line-height: 1.6;
            }
        """)
        content_layout.addWidget(info_label)
        
        main_layout.addWidget(content_widget)
        
        self.logger.info("UI界面初始化完成")
        
    def _init_crypto_data(self) -> None:
        """初始化虚拟币数据"""
        try:
            self.logger.info("开始初始化虚拟币数据...")
            
            # 这里不需要额外初始化，因为SidebarPopup已经在内部初始化了CryptoListViewModel
            # 只需要确保弹窗能够正常工作
            
            self.logger.info("虚拟币数据初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化虚拟币数据失败: {e}")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 清理资源
            if hasattr(self, 'sidebar_popup'):
                self.sidebar_popup.cleanup()
            self.logger.info("资源清理完成")
        except Exception as e:
            self.logger.error(f"清理资源失败: {e}")
        
        event.accept()


def main():
    """主函数"""
    if not GUI_AVAILABLE:
        print("GUI不可用，无法运行测试")
        return
    
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    # 创建测试窗口
    window = TestSidebarCryptoWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main()