"""
主窗口
基于MVVM模式的GUI主窗口实现
"""

from __future__ import annotations
from typing import Any, Optional, Dict, List
import logging
import sys

# 检查是否有GUI环境
try:
    from PySide6.QtWidgets import (
        QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
        QDockWidget, QMenuBar, QStatusBar, QSplitter, QTabWidget,
        QMessageBox, QPushButton
    )
    from PySide6.QtCore import Qt, QTimer, Signal
    from PySide6.QtGui import QIcon, QAction, QMouseEvent

    # 延迟导入自定义组件（在需要时导入）

    GUI_AVAILABLE = True

except ImportError as e:
    print(f"GUI库未安装: {e}")
    print("使用控制台模式...")
    GUI_AVAILABLE = False


if GUI_AVAILABLE:
    class ClickableWidget(QWidget):
        """可点击的Widget，用于检测主界面点击事件"""
        clicked = Signal()
        
        def __init__(self, parent=None):
            super().__init__(parent)
        
        def mousePressEvent(self, event: QMouseEvent) -> None:
            """鼠标按下事件"""
            if event.button() == Qt.LeftButton:
                self.clicked.emit()
            super().mousePressEvent(event)
else:
    class ClickableWidget:
        """占位符类"""
        pass

# 如果GUI不可用，保留原有的控制台实现
if not GUI_AVAILABLE:
    import threading
    import time
    from datetime import datetime

    from core.event_types import (
        BaseEvent, TickEvent, BarEvent, OrderEvent, TradeEvent,
        PositionUpdateEvent, AccountUpdateEvent, LogEvent
    )
    from core.data_types import TickData, BarData, OrderData, TradeData, PositionData, AccountData


class MainWindow(QMainWindow if GUI_AVAILABLE else object):
    """
    主窗口类
    根据GUI库可用性自动选择GUI或控制台模式
    """

    def __init__(self, event_bus: Any, config: Any):
        self.event_bus = event_bus
        self.config = config
        self.logger = logging.getLogger(__name__)

        if GUI_AVAILABLE:
            super().__init__()
            self._init_gui_mode()
        else:
            self._init_console_mode()

    def _init_gui_mode(self) -> None:
        """初始化GUI模式"""
        self.logger.info("初始化GUI模式...")

        # 获取现有的QApplication实例
        self.app = QApplication.instance()
        if self.app is None:
            self.logger.warning("QApplication未创建，GUI可能无法正常工作")

        # 延迟导入GUI组件
        from gui.views.chart_view import ChartView
        from gui.views.portfolio_view import PortfolioView
        from gui.views.trading_view import TradingView
        from gui.views.log_view import LogView
        from gui.views.crypto_list_view import CryptoListView

        from gui.viewmodels.chart_vm import ChartViewModel
        from gui.viewmodels.portfolio_vm import PortfolioViewModel
        from gui.viewmodels.trading_vm import TradingViewModel
        from gui.viewmodels.log_vm import LogViewModel
        from gui.viewmodels.crypto_list_vm import CryptoListViewModel

        # ViewModels
        self.chart_vm = ChartViewModel(self.event_bus, self.config)
        self.portfolio_vm = PortfolioViewModel(self.event_bus, self.config)
        self.trading_vm = TradingViewModel(self.event_bus, self.config)
        self.log_vm = LogViewModel(self.event_bus, self.config)
        self.crypto_list_vm = CryptoListViewModel(self.event_bus, self.config)

        # Views
        self.chart_view = ChartView()
        self.portfolio_view = PortfolioView()
        self.trading_view = TradingView()
        self.log_view = LogView()
        self.crypto_list_view = CryptoListView()

        # 连接ViewModels和Views
        self.chart_view.set_view_model(self.chart_vm)
        self.portfolio_view.set_view_model(self.portfolio_vm)
        self.trading_view.set_view_model(self.trading_vm)
        self.log_view.set_view_model(self.log_vm)
        self.crypto_list_view.set_view_model(self.crypto_list_vm)

        # 初始化UI
        self._setup_gui_ui()

    def _init_console_mode(self) -> None:
        """初始化控制台模式"""
        self.logger.info("初始化控制台模式...")

        # GUI状态
        self._running = False
        self._display_thread: Optional[threading.Thread] = None

        # 数据缓存
        self.latest_ticks: Dict[str, TickData] = {}
        self.latest_bars: Dict[str, BarData] = {}
        self.orders: List[OrderData] = []
        self.trades: List[TradeData] = []
        self.positions: List[PositionData] = []
        self.account: Optional[AccountData] = None
        self.log_messages: List[str] = []

        # 显示配置
        self.max_log_lines = 20
        self.max_orders = 10
        self.max_trades = 10
        self.refresh_interval = 2.0  # 秒

    def _setup_gui_ui(self) -> None:
        """设置GUI界面"""
        # 设置窗口属性
        self.setWindowTitle("Gemini Quant - 量化交易系统")
        self.setGeometry(100, 100, 1400, 900)

        # 创建菜单栏
        self._create_menu_bar()

        # 创建状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("系统就绪")

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建左侧边栏和弹窗
        self._create_sidebar()
        
        # 创建主内容区域（可点击关闭弹窗）
        self.main_content_widget = ClickableWidget()
        self.main_content_widget.clicked.connect(self._on_main_content_clicked)
        content_layout = QHBoxLayout(self.main_content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)

        # 左侧：图表区域
        left_splitter = QSplitter(Qt.Vertical)
        left_splitter.addWidget(self.chart_view)
        left_splitter.addWidget(self.log_view)
        left_splitter.setSizes([600, 200])

        # 创建右侧交易面板
        self._create_trading_panel()
        
        # 内容区域布局
        content_layout.addWidget(left_splitter)
        
        # 主布局添加组件
        main_layout.addWidget(self.sidebar)
        main_layout.addWidget(self.main_content_widget)

        # 连接视图间的信号
        self._connect_view_signals()

        # 虚拟币窗口（初始隐藏）
        self.crypto_window = None
        
        # 数据下载对话框（初始隐藏）
        self.data_download_dialog = None

    def _create_sidebar(self) -> None:
        """创建左侧边栏"""
        from gui.widgets import Sidebar, SidebarPopup
        
        # 创建侧边栏
        self.sidebar = Sidebar(self)
        
        # 创建弹窗（作为主窗口的子组件，悬浮显示）
        self.sidebar_popup = SidebarPopup(self)
        
        # 设置弹窗到侧边栏
        self.sidebar.set_popup_widget(self.sidebar_popup)
        
        # 连接信号
        self.sidebar.module_clicked.connect(self._on_sidebar_module_clicked)
        self.sidebar_popup.load_historical_data.connect(self._on_load_historical_data)
    
    def _create_trading_panel(self) -> None:
        """创建右侧交易面板"""
        # 移除右侧交易面板
        pass
    
    def _on_sidebar_module_clicked(self, index: int) -> None:
        """处理侧边栏模块点击"""
        try:
            self.logger.info(f"侧边栏模块被点击: {index}")
        except Exception as e:
            self.logger.error(f"处理侧边栏模块点击失败: {e}")
    
    def _on_load_historical_data(self, symbol: str, file_path: str) -> None:
        """处理历史数据加载"""
        try:
            self.logger.info(f"开始加载历史数据: {symbol} from {file_path}")
            
            # 读取parquet文件
            import pandas as pd
            df = pd.read_parquet(file_path)
            
            # 转换数据格式为图表所需的格式
            chart_data = []
            for i, row in df.iterrows():
                # 格式: (index, open, high, low, close, volume)
                chart_data.append((
                    i,  # 使用索引作为x坐标
                    float(row['open']),
                    float(row['high']),
                    float(row['low']),
                    float(row['close']),
                    float(row['volume'])
                ))
            
            # 更新图表视图
            if hasattr(self, 'chart_view') and self.chart_view:
                # 直接调用图表的_on_bars_updated方法
                self.chart_view._on_bars_updated(symbol, chart_data)
                
                self.logger.info(f"历史数据加载成功: {symbol}, 共 {len(chart_data)} 条K线")
            else:
                self.logger.warning("图表视图未初始化，无法加载历史数据")
                
        except Exception as e:
            self.logger.error(f"加载历史数据失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            if GUI_AVAILABLE:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "错误", f"加载历史数据失败:\n{str(e)}")
    

    
    def _on_main_content_clicked(self) -> None:
        """处理主内容区域点击事件"""
        try:
            # 如果侧边栏弹窗可见，则隐藏它
            if self.sidebar.is_popup_visible():
                self.sidebar.hide_popup()
                self.logger.info("点击主界面，隐藏侧边栏弹窗")
        except Exception as e:
            self.logger.error(f"处理主内容点击失败: {e}")

    def _create_menu_bar(self) -> None:
        """创建菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu('文件')

        exit_action = QAction('退出', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 视图菜单
        view_menu = menubar.addMenu('视图')

        # 工具菜单
        tools_menu = menubar.addMenu('工具')
        
        # 数据下载工具
        data_download_action = QAction('数据下载', self)
        data_download_action.triggered.connect(self._show_data_download)
        tools_menu.addAction(data_download_action)

        # 虚拟币菜单
        crypto_menu = menubar.addMenu('虚拟币')

        crypto_list_action = QAction('虚拟币列表', self)
        crypto_list_action.triggered.connect(self._show_crypto_list)
        crypto_menu.addAction(crypto_list_action)

        # 帮助菜单
        help_menu = menubar.addMenu('帮助')

        about_action = QAction('关于', self)
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)

    def _connect_view_signals(self) -> None:
        """连接视图间的信号"""
        # 图表视图的品种变化影响交易视图
        self.chart_view.symbol_changed.connect(self.trading_view.set_symbol)

        # 虚拟币列表选择品种时更新图表
        self.crypto_list_view.symbol_selected.connect(self._on_crypto_symbol_selected)

        # 可以添加更多视图间的交互

    def _show_about(self) -> None:
        """显示关于对话框"""
        QMessageBox.about(
            self,
            "关于 Gemini Quant",
            "Gemini Quant v1.0\n\n"
            "基于Python 3.13的现代化量化交易系统\n"
            "采用事件驱动架构和MVVM模式\n\n"
            "© 2024 Gemini Quant Team"
        )

    def _show_crypto_list(self) -> None:
        """显示虚拟币列表"""
        try:
            if self.crypto_window is None:
                # 创建虚拟币窗口
                self.crypto_window = QWidget()
                self.crypto_window.setWindowTitle("虚拟币列表")
                self.crypto_window.setGeometry(200, 200, 1000, 600)

                # 设置布局
                layout = QVBoxLayout(self.crypto_window)
                layout.addWidget(self.crypto_list_view)

                # 设置窗口关闭事件
                def on_close():
                    self.crypto_window = None

                self.crypto_window.closeEvent = lambda event: (on_close(), event.accept())

            # 显示窗口
            self.crypto_window.show()
            self.crypto_window.raise_()
            
        except Exception as e:
            self.logger.error(f"显示虚拟币列表失败: {e}")
            QMessageBox.warning(self, "错误", f"无法显示虚拟币列表: {str(e)}")
    
    def _show_data_download(self) -> None:
        """显示数据下载对话框"""
        try:
            if self.data_download_dialog is None:
                # 延迟导入数据下载视图
                from gui.views.data_download_view import DataDownloadDialog
                self.data_download_dialog = DataDownloadDialog(self)
                
                # 设置对话框关闭事件
                def on_close():
                    self.data_download_dialog = None
                    
                original_close = self.data_download_dialog.closeEvent
                def close_event_wrapper(event):
                    original_close(event)
                    if event.isAccepted():
                        on_close()
                        
                self.data_download_dialog.closeEvent = close_event_wrapper
            
            # 显示对话框
            self.data_download_dialog.show()
            self.data_download_dialog.raise_()
            
        except Exception as e:
            self.logger.error(f"显示数据下载对话框失败: {e}")
            QMessageBox.warning(self, "错误", f"无法显示数据下载对话框: {str(e)}")
    


    def _on_crypto_symbol_selected(self, symbol: str) -> None:
        """处理虚拟币品种选择"""
        try:
            # 更新图表显示
            self.chart_view.set_symbol(symbol)

            # 更新交易视图
            self.trading_view.set_symbol(symbol)

            self.logger.info(f"选择虚拟币品种: {symbol}")

        except Exception as e:
            self.logger.error(f"处理虚拟币品种选择失败: {e}")

    def start(self) -> None:
        """启动GUI"""
        if GUI_AVAILABLE:
            self._start_gui_mode()
        else:
            self._start_console_mode()

    def _start_gui_mode(self) -> None:
        """启动GUI模式"""
        try:
            self.logger.info("启动GUI模式...")

            # 显示窗口
            self.show()

            # 设置默认品种
            self.chart_view.set_symbol("AAPL")

            self.logger.info("GUI模式启动成功")

        except Exception as e:
            self.logger.error(f"启动GUI模式失败: {e}")
            raise

    def _start_console_mode(self) -> None:
        """启动控制台模式"""
        if self._running:
            self.logger.warning("GUI已在运行")
            return

        try:
            self.logger.info("启动控制台GUI...")

            # 订阅事件
            self._subscribe_events()

            # 启动显示线程
            self._start_display_thread()

            self._running = True
            self.logger.info("控制台GUI启动成功")

        except Exception as e:
            self.logger.error(f"启动GUI失败: {e}")
            raise

    def stop(self) -> None:
        """停止GUI"""
        if GUI_AVAILABLE:
            self._stop_gui_mode()
        else:
            self._stop_console_mode()

    def _stop_gui_mode(self) -> None:
        """停止GUI模式"""
        try:
            self.logger.info("停止GUI模式...")
            self.close()
            self.logger.info("GUI模式已停止")
        except Exception as e:
            self.logger.error(f"停止GUI模式失败: {e}")

    def _stop_console_mode(self) -> None:
        """停止控制台模式"""
        if not self._running:
            return

        try:
            self.logger.info("停止控制台GUI...")
            self._running = False

            # 停止显示线程
            if self._display_thread and self._display_thread.is_alive():
                self._display_thread.join(timeout=3)

            self.logger.info("控制台GUI已停止")

        except Exception as e:
            self.logger.error(f"停止GUI失败: {e}")

    def run(self) -> None:
        """运行GUI主循环"""
        if GUI_AVAILABLE:
            self._run_gui_mode()
        else:
            self._run_console_mode()

    def _run_gui_mode(self) -> None:
        """运行GUI模式"""
        # GUI模式下，主循环由QApplication管理
        # 这里只需要显示窗口
        self.show()
        self.logger.info("GUI窗口已显示")

    def _run_console_mode(self) -> None:
        """运行控制台模式"""
        self.logger.info("进入GUI主循环")

        try:
            # 显示欢迎信息
            self._show_welcome()

            # 主循环
            while self._running:
                time.sleep(1)

        except KeyboardInterrupt:
            self.logger.info("收到键盘中断信号")
        except Exception as e:
            self.logger.error(f"GUI主循环异常: {e}")
        finally:
            self.stop()

    def _subscribe_events(self) -> None:
        """订阅事件（控制台模式）"""
        if not GUI_AVAILABLE and self.event_bus:
            self.event_bus.subscribe("tick", self._on_tick_event, "gui_tick")
            self.event_bus.subscribe("bar", self._on_bar_event, "gui_bar")
            self.event_bus.subscribe("order", self._on_order_event, "gui_order")
            self.event_bus.subscribe("trade", self._on_trade_event, "gui_trade")
            self.event_bus.subscribe("position_update", self._on_position_event, "gui_position")
            self.event_bus.subscribe("account_update", self._on_account_event, "gui_account")
            self.event_bus.subscribe("log", self._on_log_event, "gui_log")

    def _on_tick_event(self, event: TickEvent) -> None:
        """处理Tick事件（控制台模式）"""
        if not GUI_AVAILABLE:
            self.latest_ticks[event.data.symbol] = event.data

    def _on_bar_event(self, event: BarEvent) -> None:
        """处理K线事件（控制台模式）"""
        if not GUI_AVAILABLE:
            self.latest_bars[event.data.symbol] = event.data

    def _on_order_event(self, event: OrderEvent) -> None:
        """处理订单事件（控制台模式）"""
        if not GUI_AVAILABLE:
            # 更新或添加订单
            for i, order in enumerate(self.orders):
                if order.order_id == event.data.order_id:
                    self.orders[i] = event.data
                    return

            # 新订单
            self.orders.append(event.data)

            # 限制订单数量
            if len(self.orders) > self.max_orders:
                self.orders = self.orders[-self.max_orders:]

    def _on_trade_event(self, event: TradeEvent) -> None:
        """处理成交事件（控制台模式）"""
        if not GUI_AVAILABLE:
            self.trades.append(event.data)

            # 限制成交记录数量
            if len(self.trades) > self.max_trades:
                self.trades = self.trades[-self.max_trades:]

    def _on_position_event(self, event: PositionUpdateEvent) -> None:
        """处理持仓事件（控制台模式）"""
        if not GUI_AVAILABLE:
            # 更新或添加持仓
            for i, position in enumerate(self.positions):
                if (position.symbol == event.data.symbol and
                    position.direction == event.data.direction):
                    self.positions[i] = event.data
                    return

            # 新持仓
            if event.data.volume > 0:
                self.positions.append(event.data)

    def _on_account_event(self, event: AccountUpdateEvent) -> None:
        """处理账户事件（控制台模式）"""
        if not GUI_AVAILABLE:
            self.account = event.data

    def _on_log_event(self, event: LogEvent) -> None:
        """处理日志事件（控制台模式）"""
        if not GUI_AVAILABLE:
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_line = f"[{timestamp}] {event.level}: {event.message}"
            self.log_messages.append(log_line)

            # 限制日志行数
            if len(self.log_messages) > self.max_log_lines:
                self.log_messages = self.log_messages[-self.max_log_lines:]

    def _start_display_thread(self) -> None:
        """启动显示线程（控制台模式）"""
        if not GUI_AVAILABLE:
            def display_worker():
                """显示工作线程"""
                while self._running:
                    try:
                        self._update_display()
                        time.sleep(self.refresh_interval)
                    except Exception as e:
                        self.logger.error(f"显示线程异常: {e}")
                        time.sleep(1)

            self._display_thread = threading.Thread(
                target=display_worker,
                name="GUI-Display",
                daemon=True
            )
            self._display_thread.start()

    def _show_welcome(self) -> None:
        """显示欢迎信息"""
        print("\n" + "=" * 80)
        print("🚀 Gemini Quant - 量化交易系统")
        print("=" * 80)
        print("系统正在运行，按 Ctrl+C 退出")
        print("=" * 80 + "\n")

    def _update_display(self) -> None:
        """更新显示"""
        # 清屏（简单实现）
        import os
        if os.name == 'nt':  # Windows
            os.system('cls')
        else:  # Unix/Linux/macOS
            os.system('clear')

        # 显示标题
        print("🚀 Gemini Quant - 实时监控面板")
        print("=" * 80)
        print(f"更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)

        # 显示账户信息
        self._display_account()

        # 显示持仓信息
        self._display_positions()

        # 显示最新价格
        self._display_prices()

        # 显示最近订单
        self._display_orders()

        # 显示最近成交
        self._display_trades()

        # 显示日志
        self._display_logs()

        print("=" * 80)
        print("按 Ctrl+C 退出系统")

    def _display_account(self) -> None:
        """显示账户信息"""
        print("\n💰 账户信息:")
        print("-" * 40)

        if self.account:
            print(f"账户余额: {self.account.balance:,.2f}")
            print(f"可用资金: {self.account.available:,.2f}")
            print(f"冻结资金: {self.account.frozen:,.2f}")
        else:
            print("暂无账户信息")

    def _display_positions(self) -> None:
        """显示持仓信息"""
        print("\n📊 持仓信息:")
        print("-" * 60)

        if self.positions:
            print(f"{'品种':<10} {'方向':<6} {'数量':<8} {'均价':<10} {'盈亏':<12}")
            print("-" * 60)
            for pos in self.positions:
                if pos.volume > 0:
                    print(f"{pos.symbol:<10} {pos.direction.value:<6} {pos.volume:<8} "
                          f"{pos.price:<10.2f} {pos.pnl:<12.2f}")
        else:
            print("暂无持仓")

    def _display_prices(self) -> None:
        """显示最新价格"""
        print("\n📈 最新价格:")
        print("-" * 50)

        if self.latest_ticks:
            print(f"{'品种':<10} {'最新价':<10} {'成交量':<10} {'时间':<12}")
            print("-" * 50)
            for symbol, tick in list(self.latest_ticks.items())[-5:]:  # 显示最近5个
                time_str = tick.datetime.strftime("%H:%M:%S")
                print(f"{symbol:<10} {tick.last_price:<10.2f} {tick.volume:<10} {time_str:<12}")
        else:
            print("暂无价格数据")

    def _display_orders(self) -> None:
        """显示最近订单"""
        print("\n📋 最近订单:")
        print("-" * 70)

        if self.orders:
            print(f"{'订单号':<12} {'品种':<8} {'方向':<6} {'数量':<8} {'价格':<10} {'状态':<10}")
            print("-" * 70)
            for order in self.orders[-5:]:  # 显示最近5个
                print(f"{order.order_id[-8:]:<12} {order.symbol:<8} {order.direction.value:<6} "
                      f"{order.volume:<8} {order.price:<10.2f} {order.status.value:<10}")
        else:
            print("暂无订单")

    def _display_trades(self) -> None:
        """显示最近成交"""
        print("\n💼 最近成交:")
        print("-" * 70)

        if self.trades:
            print(f"{'成交号':<12} {'品种':<8} {'方向':<6} {'数量':<8} {'价格':<10} {'时间':<12}")
            print("-" * 70)
            for trade in self.trades[-5:]:  # 显示最近5个
                time_str = trade.datetime.strftime("%H:%M:%S")
                print(f"{trade.trade_id[-8:]:<12} {trade.symbol:<8} {trade.direction.value:<6} "
                      f"{trade.volume:<8} {trade.price:<10.2f} {time_str:<12}")
        else:
            print("暂无成交")

    def _display_logs(self) -> None:
        """显示日志"""
        print("\n📝 系统日志:")
        print("-" * 80)

        if self.log_messages:
            for log_line in self.log_messages[-10:]:  # 显示最近10条
                print(log_line)
        else:
            print("暂无日志")


# 兼容性别名
ConsoleGUI = MainWindow
