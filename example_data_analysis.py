#!/usr/bin/env python3
"""
ETHUSDT 15分钟K线数据分析示例
展示如何使用获取的Parquet格式数据进行基本的技术分析
"""

import sys
import os
from pathlib import Path
from datetime import datetime, date, timedelta
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from data_service.storage import DataStorage
    from core.data_types import Interval
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_recent_data(days=30):
    """加载最近N天的数据"""
    try:
        storage_path = project_root / "data" / "bars"
        storage = DataStorage(str(storage_path), storage_type="parquet")
        
        # 获取可用日期
        dates = storage.get_available_dates("ETHUSDT")
        recent_dates = sorted(dates)[-days:]  # 最近N天
        
        logger.info(f"加载最近 {len(recent_dates)} 天的数据")
        
        all_bars = []
        for date_obj in recent_dates:
            bars = storage.load_bar_data(
                symbol="ETHUSDT",
                interval=Interval("15m"),
                start_date=date_obj,
                end_date=date_obj
            )
            all_bars.extend(bars)
        
        storage.close()
        
        # 转换为DataFrame
        if all_bars:
            data = []
            for bar in all_bars:
                data.append({
                    'datetime': bar.datetime,
                    'open': float(bar.open_price),
                    'high': float(bar.high_price),
                    'low': float(bar.low_price),
                    'close': float(bar.close_price),
                    'volume': bar.volume
                })
            
            df = pd.DataFrame(data)
            df['datetime'] = pd.to_datetime(df['datetime'])
            df = df.sort_values('datetime').reset_index(drop=True)
            
            logger.info(f"成功加载 {len(df)} 条K线数据")
            return df
        else:
            logger.error("未加载到任何数据")
            return None
            
    except Exception as e:
        logger.error(f"加载数据失败: {e}")
        return None

def calculate_technical_indicators(df):
    """计算技术指标"""
    logger.info("计算技术指标...")
    
    # 移动平均线
    df['ma5'] = df['close'].rolling(window=5).mean()
    df['ma20'] = df['close'].rolling(window=20).mean()
    df['ma60'] = df['close'].rolling(window=60).mean()
    
    # RSI
    def calculate_rsi(prices, period=14):
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    df['rsi'] = calculate_rsi(df['close'])
    
    # MACD
    def calculate_macd(prices, fast=12, slow=26, signal=9):
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        signal_line = macd.ewm(span=signal).mean()
        histogram = macd - signal_line
        return macd, signal_line, histogram
    
    df['macd'], df['macd_signal'], df['macd_histogram'] = calculate_macd(df['close'])
    
    # 布林带
    def calculate_bollinger_bands(prices, period=20, std_dev=2):
        ma = prices.rolling(window=period).mean()
        std = prices.rolling(window=period).std()
        upper = ma + (std * std_dev)
        lower = ma - (std * std_dev)
        return upper, ma, lower
    
    df['bb_upper'], df['bb_middle'], df['bb_lower'] = calculate_bollinger_bands(df['close'])
    
    # 成交量移动平均
    df['volume_ma'] = df['volume'].rolling(window=20).mean()
    
    logger.info("技术指标计算完成")
    return df

def analyze_data(df):
    """数据分析"""
    logger.info("\n=== 数据分析结果 ===")
    
    # 基本统计
    logger.info(f"数据时间范围: {df['datetime'].min()} 到 {df['datetime'].max()}")
    logger.info(f"总K线数量: {len(df)}")
    
    # 价格统计
    current_price = df['close'].iloc[-1]
    highest_price = df['high'].max()
    lowest_price = df['low'].min()
    price_change = df['close'].iloc[-1] - df['close'].iloc[0]
    price_change_pct = (price_change / df['close'].iloc[0]) * 100
    
    logger.info(f"\n价格分析:")
    logger.info(f"  当前价格: ${current_price:.2f}")
    logger.info(f"  期间最高: ${highest_price:.2f}")
    logger.info(f"  期间最低: ${lowest_price:.2f}")
    logger.info(f"  价格变化: ${price_change:.2f} ({price_change_pct:+.2f}%)")
    
    # 成交量分析
    avg_volume = df['volume'].mean()
    max_volume = df['volume'].max()
    recent_volume = df['volume'].tail(10).mean()
    
    logger.info(f"\n成交量分析:")
    logger.info(f"  平均成交量: {avg_volume:,.0f}")
    logger.info(f"  最大成交量: {max_volume:,.0f}")
    logger.info(f"  近期成交量: {recent_volume:,.0f}")
    
    # 技术指标分析
    current_rsi = df['rsi'].iloc[-1]
    current_macd = df['macd'].iloc[-1]
    current_macd_signal = df['macd_signal'].iloc[-1]
    
    logger.info(f"\n技术指标:")
    logger.info(f"  RSI: {current_rsi:.2f}")
    logger.info(f"  MACD: {current_macd:.4f}")
    logger.info(f"  MACD信号线: {current_macd_signal:.4f}")
    
    # 移动平均线分析
    ma5 = df['ma5'].iloc[-1]
    ma20 = df['ma20'].iloc[-1]
    ma60 = df['ma60'].iloc[-1]
    
    logger.info(f"\n移动平均线:")
    logger.info(f"  MA5: ${ma5:.2f}")
    logger.info(f"  MA20: ${ma20:.2f}")
    logger.info(f"  MA60: ${ma60:.2f}")
    
    # 趋势分析
    logger.info(f"\n趋势分析:")
    if current_price > ma5 > ma20 > ma60:
        logger.info("  📈 强势上涨趋势")
    elif current_price > ma5 > ma20:
        logger.info("  📈 上涨趋势")
    elif current_price < ma5 < ma20 < ma60:
        logger.info("  📉 强势下跌趋势")
    elif current_price < ma5 < ma20:
        logger.info("  📉 下跌趋势")
    else:
        logger.info("  ➡️ 震荡趋势")
    
    # RSI信号
    if current_rsi > 70:
        logger.info("  ⚠️ RSI超买信号")
    elif current_rsi < 30:
        logger.info("  ⚠️ RSI超卖信号")
    else:
        logger.info("  ✅ RSI正常范围")
    
    # MACD信号
    if current_macd > current_macd_signal:
        logger.info("  📈 MACD金叉信号")
    else:
        logger.info("  📉 MACD死叉信号")

def save_analysis_chart(df):
    """保存分析图表"""
    try:
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建子图
        fig, axes = plt.subplots(4, 1, figsize=(15, 12))
        fig.suptitle('ETHUSDT 15分钟K线技术分析', fontsize=16)
        
        # 最近200个数据点用于绘图
        plot_df = df.tail(200).copy()
        
        # 1. 价格和移动平均线
        axes[0].plot(plot_df['datetime'], plot_df['close'], label='收盘价', linewidth=1)
        axes[0].plot(plot_df['datetime'], plot_df['ma5'], label='MA5', alpha=0.7)
        axes[0].plot(plot_df['datetime'], plot_df['ma20'], label='MA20', alpha=0.7)
        axes[0].plot(plot_df['datetime'], plot_df['ma60'], label='MA60', alpha=0.7)
        axes[0].fill_between(plot_df['datetime'], plot_df['bb_upper'], plot_df['bb_lower'], alpha=0.1, label='布林带')
        axes[0].set_title('价格走势和移动平均线')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # 2. 成交量
        axes[1].bar(plot_df['datetime'], plot_df['volume'], alpha=0.6, label='成交量')
        axes[1].plot(plot_df['datetime'], plot_df['volume_ma'], color='red', label='成交量MA20')
        axes[1].set_title('成交量')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
        
        # 3. RSI
        axes[2].plot(plot_df['datetime'], plot_df['rsi'], label='RSI', color='purple')
        axes[2].axhline(y=70, color='r', linestyle='--', alpha=0.7, label='超买线')
        axes[2].axhline(y=30, color='g', linestyle='--', alpha=0.7, label='超卖线')
        axes[2].axhline(y=50, color='gray', linestyle='-', alpha=0.5)
        axes[2].set_title('RSI指标')
        axes[2].set_ylim(0, 100)
        axes[2].legend()
        axes[2].grid(True, alpha=0.3)
        
        # 4. MACD
        axes[3].plot(plot_df['datetime'], plot_df['macd'], label='MACD', color='blue')
        axes[3].plot(plot_df['datetime'], plot_df['macd_signal'], label='信号线', color='red')
        axes[3].bar(plot_df['datetime'], plot_df['macd_histogram'], alpha=0.6, label='MACD柱', color='gray')
        axes[3].axhline(y=0, color='black', linestyle='-', alpha=0.5)
        axes[3].set_title('MACD指标')
        axes[3].legend()
        axes[3].grid(True, alpha=0.3)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        chart_path = project_root / "ethusdt_analysis.png"
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        logger.info(f"分析图表已保存: {chart_path}")
        
        # 显示图表（如果在支持的环境中）
        try:
            plt.show()
        except:
            logger.info("无法显示图表，但已保存到文件")
            
    except Exception as e:
        logger.error(f"保存图表失败: {e}")

def main():
    """主函数"""
    logger.info("开始ETHUSDT数据分析示例")
    
    # 加载数据
    df = load_recent_data(days=30)
    if df is None:
        return False
    
    # 计算技术指标
    df = calculate_technical_indicators(df)
    
    # 分析数据
    analyze_data(df)
    
    # 保存分析图表
    save_analysis_chart(df)
    
    # 保存分析结果到CSV
    output_path = project_root / "ethusdt_analysis_data.csv"
    df.to_csv(output_path, index=False)
    logger.info(f"分析数据已保存: {output_path}")
    
    logger.info("\n✅ 数据分析完成！")
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 ETHUSDT数据分析示例运行成功！")
        print("查看生成的图表和CSV文件了解详细分析结果")
    else:
        print("\n❌ 分析失败，请检查日志信息")
        sys.exit(1)