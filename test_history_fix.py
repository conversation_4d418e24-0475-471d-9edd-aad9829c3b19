#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
历史数据功能修复测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from PySide6.QtCore import QTimer
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_history_functionality():
    """测试历史数据功能"""
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = QMainWindow()
    window.setWindowTitle("历史数据功能测试")
    window.setGeometry(100, 100, 800, 600)
    
    central_widget = QWidget()
    window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 创建侧边栏组件
    from gui.widgets.sidebar import Sidebar, SidebarPopup
    
    sidebar = Sidebar()
    sidebar_popup = SidebarPopup()
    
    layout.addWidget(sidebar)
    
    # 连接信号
    sidebar.module_clicked.connect(sidebar_popup.show_module)
    sidebar.popup_show_requested.connect(lambda: sidebar_popup.show())
    
    # 测试历史数据加载信号
    def on_load_historical_data(symbol, file_path):
        print(f"收到历史数据加载信号: {symbol} from {file_path}")
        
        try:
            import pandas as pd
            df = pd.read_parquet(file_path)
            print(f"成功读取历史数据: {len(df)} 条记录")
            print(f"数据列: {list(df.columns)}")
            print(f"数据样本:\n{df.head()}")
        except Exception as e:
            print(f"读取历史数据失败: {e}")
    
    sidebar_popup.load_historical_data.connect(on_load_historical_data)
    
    # 添加测试按钮
    test_btn = QPushButton("点击历史按钮测试")
    def click_history_button():
        # 模拟点击历史按钮（索引6）
        sidebar._on_button_clicked(6)
        sidebar_popup.show_module(6, sidebar.geometry())
    
    test_btn.clicked.connect(click_history_button)
    layout.addWidget(test_btn)
    
    window.show()
    
    print("历史数据功能测试窗口已启动")
    print("请点击'点击历史按钮测试'按钮来测试历史数据功能")
    print("然后在弹出的历史数据窗口中点击'加载'按钮")
    
    return app, window

if __name__ == "__main__":
    app, window = test_history_functionality()
    sys.exit(app.exec())