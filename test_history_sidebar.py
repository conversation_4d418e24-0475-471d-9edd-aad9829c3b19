#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试历史数据侧边栏功能
验证历史数据检测和加载功能
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PySide6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout
    from PySide6.QtCore import Qt
    GUI_AVAILABLE = True
except ImportError:
    print("PySide6未安装，无法运行GUI测试")
    GUI_AVAILABLE = False
    sys.exit(1)

from gui.widgets.sidebar import Sidebar, SidebarPopup
from gui.views.chart_view import ChartView
from gui.viewmodels.chart_vm import ChartViewModel
from core.event_bus import EventBus


class TestHistoryWindow(QMainWindow):
    """测试历史数据功能的窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("历史数据侧边栏测试")
        self.setGeometry(100, 100, 1400, 900)
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 创建事件总线
        self.event_bus = EventBus()
        
        self._setup_ui()
        
    def _setup_ui(self) -> None:
        """设置UI界面"""
        # 中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建侧边栏
        self.sidebar = Sidebar(self)
        
        # 创建弹窗
        self.sidebar_popup = SidebarPopup(self)
        
        # 设置弹窗到侧边栏
        self.sidebar.set_popup_widget(self.sidebar_popup)
        
        # 连接信号
        self.sidebar.module_clicked.connect(self._on_sidebar_module_clicked)
        self.sidebar_popup.load_historical_data.connect(self._on_load_historical_data)
        
        # 创建图表区域
        chart_widget = QWidget()
        chart_layout = QVBoxLayout(chart_widget)
        chart_layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建图表视图
        self.chart_view = ChartView()
        
        # 创建图表ViewModel
        config = {'gui': {'theme': 'dark'}}
        self.chart_vm = ChartViewModel(self.event_bus, config)
        self.chart_view.set_view_model(self.chart_vm)
        
        chart_layout.addWidget(self.chart_view)
        
        # 添加到主布局
        main_layout.addWidget(self.sidebar)
        main_layout.addWidget(chart_widget)
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2c3e50;
            }
            QWidget {
                background-color: #34495e;
                color: #ecf0f1;
            }
        """)
        
    def _on_sidebar_module_clicked(self, index: int) -> None:
        """处理侧边栏模块点击"""
        try:
            module_names = ["商品", "币圈", "外汇", "指标编辑", "回测", "交易", "历史"]
            if index < len(module_names):
                self.logger.info(f"侧边栏模块被点击: {module_names[index]}")
        except Exception as e:
            self.logger.error(f"处理侧边栏模块点击失败: {e}")
    
    def _on_load_historical_data(self, symbol: str, file_path: str) -> None:
        """处理历史数据加载"""
        try:
            self.logger.info(f"开始加载历史数据: {symbol} from {file_path}")
            
            # 读取parquet文件
            import pandas as pd
            df = pd.read_parquet(file_path)
            
            # 转换数据格式为图表所需的格式
            chart_data = []
            for _, row in df.iterrows():
                # 格式: (timestamp, open, high, low, close, volume)
                chart_data.append((
                    row['datetime'],
                    float(row['open']),
                    float(row['high']),
                    float(row['low']),
                    float(row['close']),
                    float(row['volume'])
                ))
            
            # 更新图表视图
            if self.chart_view:
                # 设置品种
                self.chart_view.set_symbol(symbol)
                # 更新K线数据
                self.chart_view._on_bars_updated(symbol, chart_data)
                
                self.logger.info(f"历史数据加载成功: {symbol}, 共 {len(chart_data)} 条K线")
                
                # 显示成功消息
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.information(
                    self, 
                    "加载成功", 
                    f"历史数据加载成功!\n\n品种: {symbol}\nK线数量: {len(chart_data)}\n文件: {os.path.basename(file_path)}"
                )
            else:
                self.logger.warning("图表视图未初始化，无法加载历史数据")
                
        except Exception as e:
            self.logger.error(f"加载历史数据失败: {e}")
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", f"加载历史数据失败:\n{str(e)}")


def test_history_sidebar():
    """
    测试历史数据侧边栏功能
    """
    print("🧪 开始测试历史数据侧边栏功能...")
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建QApplication
    app = QApplication(sys.argv)
    
    try:
        # 创建测试窗口
        window = TestHistoryWindow()
        window.show()
        
        print("✅ 历史数据侧边栏测试窗口已启动")
        print("📋 测试说明:")
        print("   1. 点击左侧边栏的'历史'按钮（📚图标）")
        print("   2. 在弹出的历史数据面板中查看可用的历史数据文件")
        print("   3. 点击任意历史数据的'📈 加载'按钮")
        print("   4. 观察右侧图表区域是否显示对应的K线图")
        print("   5. 检查是否显示加载成功的提示消息")
        print("\n🔍 预期结果:")
        print("   - 历史数据面板显示data/bars_yearly目录中的所有parquet文件")
        print("   - 点击加载按钮后，K线图正确显示历史数据")
        print("   - 显示加载成功的消息框")
        
        # 运行应用
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    test_history_sidebar()