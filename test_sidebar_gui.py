#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试侧边栏GUI功能
验证新增的左侧边栏和弹窗功能
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PySide6.QtWidgets import QApplication
    from PySide6.QtCore import QTimer
    GUI_AVAILABLE = True
except ImportError:
    print("PySide6未安装，无法测试GUI功能")
    GUI_AVAILABLE = False
    sys.exit(1)

from core.event_bus import EventBus
from gui.main_window import MainWindow

def test_sidebar_gui():
    """
    测试侧边栏GUI功能
    """
    print("🧪 开始测试侧边栏GUI功能...")
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建QApplication
    app = QApplication(sys.argv)
    
    try:
        # 创建事件总线
        event_bus = EventBus()
        
        # 创建配置对象（简单字典）
        config = {
            'gui': {
                'theme': 'dark',
                'refresh_interval': 1000
            },
            'data': {
                'default_symbol': 'AAPL'
            }
        }
        
        # 创建主窗口
        main_window = MainWindow(event_bus, config)
        
        print("✅ 主窗口创建成功")
        
        # 显示窗口
        main_window.show()
        print("✅ 窗口显示成功")
        
        # 设置窗口标题
        main_window.setWindowTitle("Gemini Quant - 侧边栏测试")
        
        print("\n📋 测试说明:")
        print("1. 左侧应该显示包含5个按钮的侧边栏")
        print("2. 按钮从上到下依次是：商品、币圈、外汇、指标编辑、回测")
        print("3. 点击任意按钮应该在侧边栏右侧弹出对应的内容面板")
        print("4. 弹窗右下角有关闭按钮可以关闭弹窗")
        print("5. 主界面右侧仍保留原有的交易面板和折叠功能")
        print("\n🎯 请手动测试各项功能...")
        
        # 创建定时器用于自动测试（可选）
        def auto_test():
            """自动测试功能"""
            try:
                print("\n🤖 开始自动测试...")
                
                # 测试显示第一个弹窗（商品）
                main_window._show_sidebar_popup(0)
                print("✅ 商品模块弹窗测试通过")
                
                # 延迟后测试其他弹窗
                QTimer.singleShot(2000, lambda: main_window._show_sidebar_popup(1))
                QTimer.singleShot(4000, lambda: main_window._show_sidebar_popup(2))
                QTimer.singleShot(6000, lambda: main_window._show_sidebar_popup(3))
                QTimer.singleShot(8000, lambda: main_window._show_sidebar_popup(4))
                
                # 最后隐藏弹窗
                QTimer.singleShot(10000, main_window._hide_sidebar_popup)
                
                print("🎉 自动测试序列已启动，请观察界面变化")
                
            except Exception as e:
                print(f"❌ 自动测试失败: {e}")
        
        # 延迟3秒后开始自动测试
        QTimer.singleShot(3000, auto_test)
        
        # 运行应用
        print("\n🚀 启动GUI应用...")
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """
    主函数
    """
    if not GUI_AVAILABLE:
        print("❌ GUI库不可用，无法运行测试")
        return
    
    print("🎯 Gemini Quant - 侧边栏GUI测试")
    print("=" * 50)
    
    test_sidebar_gui()

if __name__ == "__main__":
    main()