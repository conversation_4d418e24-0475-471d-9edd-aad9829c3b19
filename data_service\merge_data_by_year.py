#!/usr/bin/env python3
"""
数据合并工具
将按天保存的Parquet文件合并为按年保存
"""

import sys
import os
from pathlib import Path
from datetime import datetime, date
import pandas as pd
import logging
from typing import Dict, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from data_service.storage import DataStorage
    from core.data_types import Interval
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def merge_daily_files_to_yearly(symbol: str, interval: str = "15m", 
                                source_dir: str = None, target_dir: str = None):
    """
    将按天保存的数据文件合并为按年保存
    
    Args:
        symbol: 交易品种
        interval: 时间间隔
        source_dir: 源数据目录
        target_dir: 目标数据目录
    """
    if source_dir is None:
        source_dir = project_root / "data" / "bars" / "bars"
    else:
        source_dir = Path(source_dir)
        
    if target_dir is None:
        target_dir = project_root / "data" / "bars_yearly"
    else:
        target_dir = Path(target_dir)
        
    # 确保目标目录存在
    target_dir.mkdir(parents=True, exist_ok=True)
    
    logger.info(f"开始合并 {symbol} {interval} 数据")
    logger.info(f"源目录: {source_dir}")
    logger.info(f"目标目录: {target_dir}")
    
    # 查找所有相关的Parquet文件
    pattern = f"{symbol}_{interval}_*.parquet"
    parquet_files = list(source_dir.glob(pattern))
    
    if not parquet_files:
        logger.warning(f"未找到匹配的文件: {pattern}")
        return
        
    logger.info(f"找到 {len(parquet_files)} 个文件")
    
    # 按年份分组文件
    yearly_files: Dict[int, List[Path]] = {}
    
    for file_path in parquet_files:
        # 从文件名提取日期 (格式: SYMBOL_INTERVAL_YYYYMMDD.parquet)
        try:
            filename = file_path.stem
            date_str = filename.split('_')[-1]  # 获取日期部分
            file_date = datetime.strptime(date_str, '%Y%m%d')
            year = file_date.year
            
            if year not in yearly_files:
                yearly_files[year] = []
            yearly_files[year].append(file_path)
            
        except (ValueError, IndexError) as e:
            logger.warning(f"无法解析文件名日期: {file_path.name}, 错误: {e}")
            continue
    
    logger.info(f"数据覆盖年份: {sorted(yearly_files.keys())}")
    
    # 为每年创建合并文件
    for year, files in yearly_files.items():
        logger.info(f"\n处理 {year} 年数据，共 {len(files)} 个文件")
        
        # 读取并合并该年的所有数据
        yearly_data = []
        
        for file_path in sorted(files):
            try:
                df = pd.read_parquet(file_path)
                yearly_data.append(df)
                logger.debug(f"读取文件: {file_path.name}, 数据量: {len(df)}")
            except Exception as e:
                logger.error(f"读取文件失败: {file_path.name}, 错误: {e}")
                continue
        
        if not yearly_data:
            logger.warning(f"{year} 年没有有效数据")
            continue
            
        # 合并数据
        combined_df = pd.concat(yearly_data, ignore_index=True)
        
        # 确保时间列为datetime类型并排序
        if 'datetime' in combined_df.columns:
            combined_df['datetime'] = pd.to_datetime(combined_df['datetime'])
            combined_df = combined_df.sort_values('datetime').reset_index(drop=True)
        
        # 去重（如果有重复数据）
        before_dedup = len(combined_df)
        if 'datetime' in combined_df.columns:
            combined_df = combined_df.drop_duplicates(subset=['datetime']).reset_index(drop=True)
        after_dedup = len(combined_df)
        
        if before_dedup != after_dedup:
            logger.info(f"去重: {before_dedup} -> {after_dedup} 条记录")
        
        # 保存年度文件
        yearly_filename = f"{symbol}_{interval}_{year}.parquet"
        yearly_filepath = target_dir / yearly_filename
        
        try:
            combined_df.to_parquet(yearly_filepath, index=False)
            logger.info(f"保存年度文件: {yearly_filename}, 数据量: {len(combined_df)}")
            
            # 显示数据时间范围
            if 'datetime' in combined_df.columns:
                start_time = combined_df['datetime'].min()
                end_time = combined_df['datetime'].max()
                logger.info(f"  时间范围: {start_time} 到 {end_time}")
                
        except Exception as e:
            logger.error(f"保存年度文件失败: {yearly_filename}, 错误: {e}")
            continue
    
    logger.info(f"\n✅ {symbol} {interval} 数据合并完成！")

def verify_yearly_data(symbol: str, interval: str = "15m", data_dir: str = None):
    """
    验证年度合并数据的完整性
    
    Args:
        symbol: 交易品种
        interval: 时间间隔
        data_dir: 数据目录
    """
    if data_dir is None:
        data_dir = project_root / "data" / "bars_yearly"
    else:
        data_dir = Path(data_dir)
    
    logger.info(f"\n=== 验证 {symbol} {interval} 年度数据 ===")
    
    # 查找年度文件
    pattern = f"{symbol}_{interval}_*.parquet"
    yearly_files = list(data_dir.glob(pattern))
    
    if not yearly_files:
        logger.warning(f"未找到年度文件: {pattern}")
        return
    
    total_records = 0
    years_data = {}
    
    for file_path in sorted(yearly_files):
        try:
            # 提取年份
            filename = file_path.stem
            year = int(filename.split('_')[-1])
            
            # 读取数据
            df = pd.read_parquet(file_path)
            record_count = len(df)
            total_records += record_count
            
            # 获取时间范围
            if 'datetime' in df.columns:
                df['datetime'] = pd.to_datetime(df['datetime'])
                start_time = df['datetime'].min()
                end_time = df['datetime'].max()
                time_range = f"{start_time.strftime('%Y-%m-%d')} 到 {end_time.strftime('%Y-%m-%d')}"
            else:
                time_range = "无时间信息"
            
            years_data[year] = {
                'records': record_count,
                'time_range': time_range,
                'file_size': file_path.stat().st_size / 1024 / 1024  # MB
            }
            
            logger.info(f"{year} 年: {record_count:,} 条记录, {time_range}, 文件大小: {years_data[year]['file_size']:.1f}MB")
            
        except Exception as e:
            logger.error(f"验证文件失败: {file_path.name}, 错误: {e}")
    
    logger.info(f"\n总计: {len(years_data)} 个年度文件, {total_records:,} 条记录")
    
    # 检查数据连续性
    if len(years_data) > 1:
        years = sorted(years_data.keys())
        missing_years = []
        for i in range(years[0], years[-1] + 1):
            if i not in years:
                missing_years.append(i)
        
        if missing_years:
            logger.warning(f"缺失年份: {missing_years}")
        else:
            logger.info("✅ 年度数据连续")

def cleanup_daily_files(symbol: str, interval: str = "15m", 
                       source_dir: str = None, backup: bool = True):
    """
    清理按天保存的文件（可选备份）
    
    Args:
        symbol: 交易品种
        interval: 时间间隔
        source_dir: 源数据目录
        backup: 是否备份到backup目录
    """
    if source_dir is None:
        source_dir = project_root / "data" / "bars"
    else:
        source_dir = Path(source_dir)
    
    logger.info(f"\n=== 清理 {symbol} {interval} 按天文件 ===")
    
    # 查找要清理的文件
    pattern = f"{symbol}_{interval}_*.parquet"
    daily_files = list(source_dir.glob(pattern))
    
    if not daily_files:
        logger.info("没有找到需要清理的文件")
        return
    
    logger.info(f"找到 {len(daily_files)} 个按天文件")
    
    # 备份文件
    if backup:
        backup_dir = source_dir.parent / "bars_daily_backup"
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"备份文件到: {backup_dir}")
        
        for file_path in daily_files:
            try:
                backup_path = backup_dir / file_path.name
                import shutil
                shutil.copy2(file_path, backup_path)
                logger.debug(f"备份: {file_path.name}")
            except Exception as e:
                logger.error(f"备份文件失败: {file_path.name}, 错误: {e}")
                return
    
    # 删除原文件
    deleted_count = 0
    for file_path in daily_files:
        try:
            file_path.unlink()
            deleted_count += 1
            logger.debug(f"删除: {file_path.name}")
        except Exception as e:
            logger.error(f"删除文件失败: {file_path.name}, 错误: {e}")
    
    logger.info(f"✅ 清理完成，删除 {deleted_count} 个文件")
    if backup:
        logger.info(f"备份保存在: {backup_dir}")

def main():
    """
    主函数
    """
    logger.info("开始数据合并流程")
    
    # 配置参数
    symbol = "ETHUSDT"
    interval = "15m"
    
    try:
        # 1. 合并数据
        merge_daily_files_to_yearly(symbol, interval)
        
        # 2. 验证合并结果
        verify_yearly_data(symbol, interval)
        
        # 3. 询问是否清理原文件
        response = input("\n是否清理原始按天文件？(y/N): ").strip().lower()
        if response in ['y', 'yes']:
            cleanup_daily_files(symbol, interval, backup=True)
        else:
            logger.info("保留原始按天文件")
        
        logger.info("\n🎉 数据合并流程完成！")
        
    except Exception as e:
        logger.error(f"数据合并失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 数据合并成功！")
    else:
        print("\n❌ 数据合并失败！")
        sys.exit(1)