#!/usr/bin/env python3
"""
检查ETHUSDT数据文件的实际结构
"""

import pandas as pd
from pathlib import Path

# 获取第一个数据文件
data_path = Path("data/bars/bars")
parquet_files = list(data_path.glob("ETHUSDT_15m_*.parquet"))

if parquet_files:
    first_file = sorted(parquet_files)[0]
    print(f"检查文件: {first_file}")
    
    # 读取文件
    df = pd.read_parquet(first_file)
    
    print(f"\n文件信息:")
    print(f"行数: {len(df)}")
    print(f"列数: {len(df.columns)}")
    print(f"\n列名: {list(df.columns)}")
    print(f"\n数据类型:")
    print(df.dtypes)
    print(f"\n前5行数据:")
    print(df.head())
    
    if len(df) > 0:
        print(f"\n数据示例:")
        for col in df.columns:
            print(f"{col}: {df[col].iloc[0]}")
else:
    print("未找到数据文件")