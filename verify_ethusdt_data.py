#!/usr/bin/env python3
"""
验证ETHUSDT 15分钟K线数据
检查数据质量和完整性，并显示基本统计信息
"""

import sys
import os
from pathlib import Path
from datetime import datetime, date
import pandas as pd
import pyarrow.parquet as pq
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from data_service.storage import DataStorage
    from core.data_types import Interval
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def analyze_parquet_files():
    """分析Parquet文件"""
    data_path = project_root / "data" / "bars" / "bars"
    
    if not data_path.exists():
        logger.error(f"数据目录不存在: {data_path}")
        return False
    
    # 获取所有ETHUSDT 15分钟数据文件
    parquet_files = list(data_path.glob("ETHUSDT_15m_*.parquet"))
    
    if not parquet_files:
        logger.error("未找到ETHUSDT 15分钟数据文件")
        return False
    
    logger.info(f"找到 {len(parquet_files)} 个数据文件")
    
    # 按日期排序
    parquet_files.sort()
    
    # 读取第一个和最后一个文件查看数据范围
    first_file = parquet_files[0]
    last_file = parquet_files[-1]
    
    logger.info(f"第一个文件: {first_file.name}")
    logger.info(f"最后一个文件: {last_file.name}")
    
    # 读取几个文件查看数据结构
    sample_files = [first_file, parquet_files[len(parquet_files)//2], last_file]
    
    total_records = 0
    all_data = []
    
    for i, file_path in enumerate(sample_files):
        try:
            df = pd.read_parquet(file_path)
            total_records += len(df)
            all_data.append(df)
            
            logger.info(f"\n文件 {i+1}: {file_path.name}")
            logger.info(f"记录数: {len(df)}")
            logger.info(f"列名: {list(df.columns)}")
            
            if len(df) > 0:
                logger.info(f"时间范围: {df['datetime'].min()} 到 {df['datetime'].max()}")
                logger.info(f"价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
                logger.info(f"成交量范围: {df['volume'].min()} - {df['volume'].max()}")
                
                # 显示前几条记录
                logger.info("前3条记录:")
                print(df.head(3).to_string())
                
        except Exception as e:
            logger.error(f"读取文件失败 {file_path}: {e}")
    
    # 合并数据进行整体分析
    if all_data:
        combined_df = pd.concat(all_data, ignore_index=True)
        combined_df['datetime'] = pd.to_datetime(combined_df['datetime'])
        combined_df = combined_df.sort_values('datetime')
        
        logger.info(f"\n=== 数据整体统计 ===")
        logger.info(f"总记录数: {len(combined_df)}")
        logger.info(f"时间跨度: {combined_df['datetime'].min()} 到 {combined_df['datetime'].max()}")
        logger.info(f"价格统计:")
        logger.info(f"  最低价: {combined_df['low'].min():.2f}")
        logger.info(f"  最高价: {combined_df['high'].max():.2f}")
        logger.info(f"  平均收盘价: {combined_df['close'].mean():.2f}")
        logger.info(f"成交量统计:")
        logger.info(f"  总成交量: {combined_df['volume'].sum():,}")
        logger.info(f"  平均成交量: {combined_df['volume'].mean():.0f}")
        
        # 检查数据完整性
        logger.info(f"\n=== 数据完整性检查 ===")
        
        # 检查缺失值
        missing_data = combined_df.isnull().sum()
        if missing_data.sum() > 0:
            logger.warning(f"发现缺失值: {missing_data[missing_data > 0].to_dict()}")
        else:
            logger.info("✅ 无缺失值")
        
        # 检查时间间隔
        time_diffs = combined_df['datetime'].diff().dropna()
        expected_interval = pd.Timedelta(minutes=15)
        irregular_intervals = time_diffs[time_diffs != expected_interval]
        
        if len(irregular_intervals) > 0:
            logger.warning(f"发现 {len(irregular_intervals)} 个不规则时间间隔")
            logger.info(f"不规则间隔示例: {irregular_intervals.head().tolist()}")
        else:
            logger.info("✅ 时间间隔规律")
        
        # 检查价格逻辑
        price_errors = combined_df[
            (combined_df['high'] < combined_df['low']) |
            (combined_df['open'] > combined_df['high']) |
            (combined_df['open'] < combined_df['low']) |
            (combined_df['close'] > combined_df['high']) |
            (combined_df['close'] < combined_df['low'])
        ]
        
        if len(price_errors) > 0:
            logger.warning(f"发现 {len(price_errors)} 条价格逻辑错误的记录")
        else:
            logger.info("✅ 价格数据逻辑正确")
    
    return True

def load_data_using_storage():
    """使用DataStorage类加载数据"""
    try:
        storage_path = project_root / "data" / "bars"
        storage = DataStorage(str(storage_path), storage_type="parquet")
        
        # 获取可用的交易品种
        symbols = storage.get_available_symbols()
        logger.info(f"可用交易品种: {symbols}")
        
        if "ETHUSDT" in symbols:
            # 获取ETHUSDT的可用日期
            dates = storage.get_available_dates("ETHUSDT")
            logger.info(f"ETHUSDT数据日期范围: {min(dates)} 到 {max(dates)}")
            logger.info(f"总共 {len(dates)} 天的数据")
            
            # 加载最近几天的数据进行验证
            recent_dates = sorted(dates)[-3:]  # 最近3天
            
            for test_date in recent_dates:
                bars = storage.load_bar_data(
                    symbol="ETHUSDT",
                    interval=Interval("15m"),
                    start_date=test_date,
                    end_date=test_date
                )
                
                if bars:
                    logger.info(f"日期 {test_date}: 加载了 {len(bars)} 条K线数据")
                    # 显示第一条数据
                    first_bar = bars[0]
                    logger.info(f"  示例数据: {first_bar.datetime} O:{first_bar.open_price} H:{first_bar.high_price} L:{first_bar.low_price} C:{first_bar.close_price} V:{first_bar.volume}")
                else:
                    logger.warning(f"日期 {test_date}: 未加载到数据")
        
        storage.close()
        return True
        
    except Exception as e:
        logger.error(f"使用DataStorage加载数据失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始验证ETHUSDT 15分钟K线数据")
    
    # 分析Parquet文件
    logger.info("\n=== 直接分析Parquet文件 ===")
    if not analyze_parquet_files():
        return False
    
    # 使用DataStorage类加载数据
    logger.info("\n=== 使用DataStorage类验证 ===")
    if not load_data_using_storage():
        return False
    
    logger.info("\n✅ 数据验证完成！")
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 ETHUSDT 15分钟K线数据验证成功！")
        print("数据质量良好，可以用于量化分析和策略回测")
    else:
        print("\n❌ 数据验证失败，请检查日志信息")
        sys.exit(1)