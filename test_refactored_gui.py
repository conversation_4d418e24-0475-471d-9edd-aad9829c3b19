#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构后的GUI测试脚本
测试左侧边栏悬浮弹窗和右侧可折叠交易面板功能
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from PySide6.QtWidgets import QApplication
    from PySide6.QtCore import QTimer
    
    from core.event_bus import EventBus
    from gui.main_window import MainWindow
    from infrastructure.config_manager import ConfigManager
    
    def test_refactored_gui():
        """测试重构后的GUI功能"""
        print("🎯 Gemini Quant - 重构后GUI测试")
        print("=" * 50)
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建配置
        config = ConfigManager()
        
        # 创建事件总线
        event_bus = EventBus()
        
        # 创建主窗口
        main_window = MainWindow(event_bus, config)
        
        print("✅ 主窗口创建成功")
        
        # 显示窗口
        main_window.show()
        print("✅ 主窗口显示成功")
        
        # 自动测试序列
        def auto_test_sequence():
            """自动测试序列"""
            print("\n🔄 开始自动测试序列...")
            
            # 测试侧边栏模块
            modules = ["商品", "币圈", "外汇", "指标编辑", "回测", "交易"]
            
            def test_module(index):
                if index < len(modules):
                    print(f"📦 测试 {modules[index]} 模块...")
                    # 模拟点击侧边栏按钮
                    if hasattr(main_window, 'sidebar') and main_window.sidebar.buttons:
                        main_window.sidebar.buttons[index].click()
                    
                    # 延迟后测试下一个模块
                    QTimer.singleShot(2000, lambda: test_module(index + 1))
                else:
                    print("\n✅ 自动测试序列完成")
                    print("\n📋 测试说明:")
                    print("1. 左侧边栏有6个功能模块按钮（包括新增的'交易'按钮）")
                    print("2. 点击按钮会在右侧显示悬浮弹窗")
                    print("3. 点击主界面区域可关闭弹窗")
                    print("4. '交易'模块的弹窗为空内容")
                    print("5. 右侧交易面板已移除")
                    print("\n🎮 请手动测试各项功能...")
            
            # 开始测试
            test_module(0)
        
        # 延迟启动自动测试
        QTimer.singleShot(1000, auto_test_sequence)
        
        # 运行应用程序
        return app.exec()
    
    if __name__ == "__main__":
        sys.exit(test_refactored_gui())
        
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保已安装PySide6: pip install PySide6")
    sys.exit(1)
except Exception as e:
    print(f"❌ 运行错误: {e}")
    sys.exit(1)