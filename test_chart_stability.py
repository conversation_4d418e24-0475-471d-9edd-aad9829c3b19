#!/usr/bin/env python3
"""
测试图表稳定性 - 验证坐标轴飞速移动问题是否已修复
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

try:
    from PySide6.QtWidgets import QApplication, QVBoxLayout, QWidget, QPushButton, QLabel
    from PySide6.QtCore import QTimer
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False
    print("PySide6未安装，无法测试GUI")
    sys.exit(1)

from gui.views.chart_view import ChartView
from gui.viewmodels.chart_vm import ChartViewModel
from core.event_bus import EventBus
from datetime import datetime, timedelta
import random


class ChartStabilityTest:
    """图表稳定性测试"""
    
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.event_bus = EventBus()
        
        # 创建配置
        self.config = {
            'data_service': {
                'storage_path': './data',
                'storage_type': 'feather'
            }
        }
        
        # 创建图表组件
        self.chart_vm = ChartViewModel(self.event_bus, self.config)
        self.chart_view = ChartView()
        self.chart_view.set_view_model(self.chart_vm)
        
        # 设置窗口
        self.chart_view.setWindowTitle("图表稳定性测试 - 检查坐标轴是否还会飞速移动")
        self.chart_view.resize(1200, 800)
        
        # 创建控制面板
        self.control_widget = QWidget()
        control_layout = QVBoxLayout(self.control_widget)
        
        self.status_label = QLabel("准备开始测试...")
        control_layout.addWidget(self.status_label)
        
        start_btn = QPushButton("开始数据流测试")
        start_btn.clicked.connect(self.start_test)
        control_layout.addWidget(start_btn)
        
        stop_btn = QPushButton("停止测试")
        stop_btn.clicked.connect(self.stop_test)
        control_layout.addWidget(stop_btn)
        
        self.control_widget.setWindowTitle("测试控制")
        self.control_widget.resize(300, 150)
        
        # 测试数据
        self.base_price = 100.0
        self.data_count = 0
        self.chart_data_history = []
        
        # 数据生成定时器
        self.data_timer = QTimer()
        self.data_timer.timeout.connect(self._generate_data)
        
    def start_test(self):
        """开始测试"""
        print("开始图表稳定性测试...")
        print("观察图表坐标轴是否还会出现飞速移动问题")
        print("修复内容:")
        print("1. 降低数据更新频率 (500ms -> 2000ms)")
        print("2. 增加自动缩放时间间隔 (1s -> 2s)")
        print("3. 只在数据真正变化时才缩放")
        print("4. 使用更温和的缩放方式")
        print("5. 添加自动缩放开关控制")
        
        self.status_label.setText("测试进行中... 观察坐标轴行为")
        self.data_timer.start(1000)  # 每秒生成一次数据
        
    def stop_test(self):
        """停止测试"""
        self.data_timer.stop()
        self.status_label.setText("测试已停止")
        print("测试停止")
        
    def _generate_data(self):
        """生成测试数据"""
        try:
            # 生成随机价格变化
            price_change = random.uniform(-1, 1)
            self.base_price += price_change
            
            # 确保价格为正数
            if self.base_price < 50:
                self.base_price = 50
            elif self.base_price > 200:
                self.base_price = 200
            
            open_price = self.base_price
            high_price = open_price + random.uniform(0, 2)
            low_price = open_price - random.uniform(0, 2)
            close_price = open_price + random.uniform(-1, 1)
            volume = random.randint(1000, 10000)
            
            # 创建时间戳（递增）
            current_time = datetime.now() - timedelta(minutes=100-self.data_count)
            timestamp = current_time.timestamp()
            
            # 创建图表数据
            chart_data_point = (
                timestamp,
                open_price,
                high_price,
                low_price,
                close_price,
                volume
            )
            
            # 添加到历史数据
            self.chart_data_history.append(chart_data_point)
            
            # 保持最近100条数据
            if len(self.chart_data_history) > 100:
                self.chart_data_history.pop(0)
            
            # 更新图表
            self.chart_view._on_bars_updated("TEST", self.chart_data_history.copy())
            
            self.data_count += 1
            
            # 更新状态
            self.status_label.setText(f"已生成 {self.data_count} 条数据，当前价格: {close_price:.2f}")
            
            # 限制测试时间
            if self.data_count > 200:
                self.stop_test()
                self.status_label.setText("测试完成！如果坐标轴没有飞速移动，说明修复成功")
                
        except Exception as e:
            print(f"生成测试数据失败: {e}")
            self.stop_test()
    
    def run(self):
        """运行测试"""
        print("=" * 60)
        print("图表稳定性测试")
        print("=" * 60)
        print("此测试用于验证图表坐标轴飞速移动问题是否已修复")
        print("请观察:")
        print("1. 横坐标轴是否还会向左飞速移动")
        print("2. 纵坐标轴是否还会向上飞速移动")
        print("3. 图表是否会白屏无响应")
        print("4. 可以使用'自动缩放'按钮来控制缩放行为")
        print("=" * 60)
        
        self.chart_view.show()
        self.control_widget.show()
        
        return self.app.exec()


if __name__ == "__main__":
    test = ChartStabilityTest()
    sys.exit(test.run())
