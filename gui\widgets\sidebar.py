#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
左侧边栏组件
独立的侧边栏实现，包含模块按钮和悬浮弹窗
"""

from __future__ import annotations
from typing import Any, Optional, List, Callable
import logging

try:
    from PySide6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
        QStackedWidget, QFrame, QLineEdit, QScrollArea, QTableWidget,
        QTableWidgetItem, QHeaderView, QAbstractItemView
    )
    from PySide6.QtCore import Qt, Signal, QRect
    from PySide6.QtGui import QMouseEvent, QFont
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False

try:
    from gui.viewmodels.crypto_list_vm import CryptoListViewModel
    from data_service.connectors.binance_connector import BinanceConnector
    from core.event_bus import EventBus
    import yaml
    import os
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False


class SidebarPopup(QWidget):
    """
    侧边栏弹窗组件
    悬浮在主界面上方的弹窗
    
    可调节参数说明：
    - 弹窗尺寸：在 Sidebar 类中的 POPUP_WIDTH 和 POPUP_HEIGHT 常量
    - 标题字体大小：修改 title_label 的 font-size 样式
    - 内容字体大小：修改各内容组件的 font-size 样式
    - 弹窗位置：在 show_module 方法中调整 x, y 坐标计算
    """
    
    # 信号定义
    close_requested = Signal()
    load_historical_data = Signal(str, str)  # 加载历史数据信号 (symbol, file_path)
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.module_titles = ["商品交易", "加密货币", "外汇交易", "指标编辑器", "策略回测", "交易", "历史数据"]
        self._setup_ui()
        self._current_index = -1
        
        # 虚拟币数据管理
        self.crypto_vm: Optional[CryptoListViewModel] = None
        self.crypto_data: List[Dict[str, Any]] = []
        self._init_crypto_data()
        
        # 自选品种管理
        self.favorite_symbols = ['SOLUSDT', 'ETHUSDT', 'BTCUSDT']  # 默认自选品种
        self.current_view_mode = {}  # 记录每个模块的当前视图模式（全部/自选）
        
        # 设置窗口属性
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.hide()
    
    def _setup_ui(self) -> None:
        """设置UI界面"""
        # 弹窗尺寸 - 从父组件获取尺寸参数，如需调整请修改 Sidebar 类中的常量
        if hasattr(self.parent(), 'POPUP_WIDTH') and hasattr(self.parent(), 'POPUP_HEIGHT'):
            popup_width = self.parent().POPUP_WIDTH
            popup_height = self.parent().POPUP_HEIGHT
            # 如果高度设置为-1，则使用与侧边栏同高
            if popup_height == -1 and self.parent().parent():
                popup_height = self.parent().parent().height()
            self.setFixedSize(popup_width, popup_height)
        else:
            self.setFixedSize(400, 500)  # 默认尺寸
        
        self.setStyleSheet("""
            QWidget {
                background-color: rgba(52, 73, 94, 240);
                border: 1px solid #2c3e50;
                border-radius: 10px;
            }
        """)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)
        
        # 标题 - 修改 font-size 可以调节标题字体大小
        self.title_label = QLabel("")
        self.title_label.setStyleSheet("""
            QLabel {
                color: #ecf0f1;
                font-size: 18px;  /* 标题字体大小 - 可在此调节 */
                font-weight: bold;
                padding: 10px 0;
                border-bottom: 2px solid #3498db;
                background-color: transparent;
                border-radius: 0px;
            }
        """)
        main_layout.addWidget(self.title_label)
        
        # 内容区域
        self.content_stack = QStackedWidget()
        main_layout.addWidget(self.content_stack)
        
        # 创建内容页面
        self._create_content_pages()
        
        # 添加弹簧
        main_layout.addStretch()
    
    def _create_content_pages(self) -> None:
        """创建内容页面"""
        # 为前三个模块（商品、币圈、外汇）创建数据表格页面
        for i in range(3):
            page = self._create_trading_page(i)
            self.content_stack.addWidget(page)
        
        # 为其他模块创建原有的文本页面
        other_pages_content = [
            ("指标编辑器", "📊 技术指标编辑器\n\n🔹 指标开发：\n• 自定义指标编写\n• 可视化指标构建\n• 指标参数优化\n• 多周期指标分析\n\n🔹 策略构建：\n• 信号生成逻辑\n• 多指标组合\n• 条件触发设置\n• 策略回测验证\n\n🔹 高级功能：\n• 机器学习指标\n• 量价分析\n• 市场微观结构"),
            ("策略回测", "📈 策略回测模块\n\n🔹 回测功能：\n• 历史数据回测\n• 实时模拟交易\n• 多策略对比\n• 参数敏感性分析\n\n🔹 绩效分析：\n• 收益率分析\n• 风险指标计算\n• 最大回撤分析\n• 夏普比率评估\n\n🔹 优化工具：\n• 遗传算法优化\n• 网格搜索\n• 贝叶斯优化\n• 机器学习优化"),
            ("交易", "")
        ]
        
        # 添加文本页面（索引3,4,5）
        for title, content in other_pages_content:
            page = QWidget()
            page_layout = QVBoxLayout(page)
            page_layout.setContentsMargins(0, 0, 0, 0)
            
            content_label = QLabel(content)
            content_label.setStyleSheet("""
                QLabel {
                    color: #bdc3c7;
                    font-size: 13px;  /* 内容字体大小 - 可在此调节 */
                    line-height: 1.6;
                    padding: 15px;
                    background-color: rgba(44, 62, 80, 180);
                    border-radius: 8px;
                    border: 1px solid rgba(52, 152, 219, 100);
                }
            """)
            content_label.setWordWrap(True)
            content_label.setAlignment(Qt.AlignTop)
            
            page_layout.addWidget(content_label)
            page_layout.addStretch()
            
            self.content_stack.addWidget(page)
        
        # 为历史数据模块创建专门的页面（索引6）
        history_page = self._create_history_page()
        self.content_stack.addWidget(history_page)
    
    def _init_crypto_data(self) -> None:
        """初始化虚拟币数据"""
        if not CRYPTO_AVAILABLE:
            self.logger.warning("虚拟币模块不可用")
            return
        
        try:
            # 加载配置文件
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config', 'default_config.yaml')
            config = {}
            
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                self.logger.info(f"已加载配置文件: {config_path}")
            else:
                # 使用默认配置
                config = {
                    'data_service': {
                        'connectors': {
                            'binance': {
                                'enabled': True,
                                'api_key': '',
                                'api_secret': '',
                                'testnet': False
                            }
                        }
                    }
                }
                self.logger.warning(f"配置文件不存在，使用默认配置: {config_path}")
            
            # 创建事件总线
            event_bus = EventBus()
            
            # 创建虚拟币ViewModel
            self.crypto_vm = CryptoListViewModel(event_bus, config)
            
            # 连接信号
            if self.crypto_vm:
                self.crypto_vm.prices_updated.connect(self._on_crypto_data_updated)
                self.crypto_vm.error_occurred.connect(self._on_crypto_error)
                self.logger.info("虚拟币数据管理器初始化成功")
                
        except Exception as e:
            self.logger.error(f"初始化虚拟币数据失败: {e}")
            # 创建一个简单的备用配置
            try:
                event_bus = EventBus()
                config = {
                    'data_service': {
                        'connectors': {
                            'binance': {
                                'enabled': True,
                                'api_key': '',
                                'api_secret': '',
                                'testnet': False
                            }
                        }
                    }
                }
                self.crypto_vm = CryptoListViewModel(event_bus, config)
                if self.crypto_vm:
                    self.crypto_vm.prices_updated.connect(self._on_crypto_data_updated)
                    self.crypto_vm.error_occurred.connect(self._on_crypto_error)
                    self.logger.info("使用备用配置初始化虚拟币数据管理器")
            except Exception as backup_e:
                self.logger.error(f"备用配置初始化也失败: {backup_e}")
    
    def _on_crypto_data_updated(self, data: List[Dict[str, Any]]) -> None:
        """虚拟币数据更新回调"""
        try:
            self.crypto_data = data[:20]  # 只取前20个
            # 如果当前显示的是币圈模块，更新表格
            if self._current_index == 1:  # 币圈是第二个模块（索引1）
                self._refresh_current_table()
        except Exception as e:
            self.logger.error(f"更新虚拟币数据失败: {e}")
    
    def _on_crypto_error(self, error_msg: str) -> None:
        """虚拟币数据错误回调"""
        self.logger.error(f"虚拟币数据错误: {error_msg}")
    
    def _on_search_changed(self, text: str, module_index: int) -> None:
        """处理搜索文本变化"""
        try:
            # 如果当前显示的模块与搜索框对应的模块一致，则更新表格
            if hasattr(self, '_current_index') and self._current_index == module_index:
                current_page = self.content_stack.currentWidget()
                if current_page:
                    table = current_page.findChild(QTableWidget)
                    if table:
                        self._populate_table_data(table, module_index, text)
        except Exception as e:
            self.logger.error(f"处理搜索变化失败: {e}")
    
    def _on_view_mode_changed(self, module_index: int, mode: str) -> None:
        """处理视图模式切换（全部/自选）"""
        self.current_view_mode[module_index] = mode
        
        # 更新按钮样式
        if hasattr(self, 'view_buttons') and module_index in self.view_buttons:
            buttons = self.view_buttons[module_index]
            
            # 重置所有按钮样式
            for btn_mode, btn in buttons.items():
                if btn_mode == mode:
                    # 激活状态
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #3498db;
                            color: white;
                            border: none;
                            padding: 5px 15px;
                            border-radius: 3px;
                            font-size: 12px;
                            font-weight: bold;
                        }
                        QPushButton:hover {
                            background-color: #2980b9;
                        }
                    """)
                else:
                    # 非激活状态
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: rgba(52, 73, 94, 180);
                            color: #bdc3c7;
                            border: 1px solid #34495e;
                            padding: 5px 15px;
                            border-radius: 3px;
                            font-size: 12px;
                        }
                        QPushButton:hover {
                            background-color: #34495e;
                            color: white;
                        }
                    """)
        
        # 刷新表格数据
        if hasattr(self, '_current_index') and self._current_index == module_index:
            self._refresh_current_table()
    
    def _refresh_current_table(self) -> None:
        """刷新当前显示的表格"""
        try:
            if (hasattr(self, '_current_index') and self._current_index >= 0 and 
                self.content_stack.currentWidget()):
                # 找到当前页面的表格并更新
                current_page = self.content_stack.currentWidget()
                table = current_page.findChild(QTableWidget)
                if table:
                    # 获取当前搜索文本
                    search_text = ""
                    if (hasattr(self, 'search_boxes') and 
                        self._current_index in self.search_boxes):
                        search_text = self.search_boxes[self._current_index].text()
                    
                    self._populate_table_data(table, self._current_index, search_text)
        except Exception as e:
            self.logger.error(f"刷新表格失败: {e}")
    
    def _create_trading_page(self, module_index: int) -> QWidget:
        """创建交易页面（商品、币圈、外汇）"""
        page = QWidget()
        page_layout = QVBoxLayout(page)
        page_layout.setContentsMargins(10, 10, 10, 10)
        page_layout.setSpacing(10)
        
        # 分类标签区域
        category_layout = QHBoxLayout()
        category_layout.setSpacing(5)
        
        # 全部按钮
        all_btn = QPushButton("全部")
        all_btn.setObjectName(f"all_btn_{module_index}")
        all_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 3px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        # 自选按钮
        favorite_btn = QPushButton("自选")
        favorite_btn.setObjectName(f"favorite_btn_{module_index}")
        favorite_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(52, 73, 94, 180);
                color: #bdc3c7;
                border: 1px solid #34495e;
                padding: 5px 15px;
                border-radius: 3px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #34495e;
                color: white;
            }
        """)
        
        # 连接按钮点击事件
        all_btn.clicked.connect(lambda: self._on_view_mode_changed(module_index, 'all'))
        favorite_btn.clicked.connect(lambda: self._on_view_mode_changed(module_index, 'favorite'))
        
        # 存储按钮引用
        if not hasattr(self, 'view_buttons'):
            self.view_buttons = {}
        self.view_buttons[module_index] = {'all': all_btn, 'favorite': favorite_btn}
        
        category_layout.addWidget(all_btn)
        category_layout.addWidget(favorite_btn)
        category_layout.addStretch()
        
        # 搜索框
        search_box = QLineEdit()
        search_box.setPlaceholderText("搜索...")
        search_box.setStyleSheet("""
            QLineEdit {
                background-color: rgba(44, 62, 80, 180);
                color: #ecf0f1;
                border: 1px solid #34495e;
                border-radius: 5px;
                padding: 8px 30px 8px 10px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border: 1px solid #3498db;
            }
        """)
        
        # 连接搜索功能
        search_box.textChanged.connect(lambda text: self._on_search_changed(text, module_index))
        
        # 搜索按钮
        search_btn = QPushButton("🔍")
        search_btn.setFixedSize(25, 25)
        search_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #bdc3c7;
                border: none;
                font-size: 14px;
            }
            QPushButton:hover {
                color: #3498db;
            }
        """)
        
        # 搜索区域布局
        search_layout = QHBoxLayout()
        search_layout.addWidget(search_box)
        search_layout.addWidget(search_btn)
        
        # 存储搜索框引用以便后续使用
        if not hasattr(self, 'search_boxes'):
            self.search_boxes = {}
        self.search_boxes[module_index] = search_box
        
        category_layout.addLayout(search_layout)
        page_layout.addLayout(category_layout)
        
        # 数据表格
        table = QTableWidget()
        table.setColumnCount(3)
        table.setHorizontalHeaderLabels(["品种/代码", "价格", "涨跌幅"])
        
        # 表格样式
        table.setStyleSheet("""
            QTableWidget {
                background-color: rgba(44, 62, 80, 180);
                color: #ecf0f1;
                border: 1px solid #34495e;
                border-radius: 5px;
                gridline-color: #34495e;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #34495e;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
            }
            QHeaderView::section {
                background-color: #2c3e50;
                color: #ecf0f1;
                padding: 8px;
                border: none;
                border-bottom: 2px solid #3498db;
                font-weight: bold;
                font-size: 12px;
            }
        """)
        
        # 设置表格属性
        table.horizontalHeader().setStretchLastSection(True)
        table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        table.setSelectionBehavior(QAbstractItemView.SelectRows)
        table.setAlternatingRowColors(True)
        table.verticalHeader().setVisible(False)
        
        # 填充示例数据
        self._populate_table_data(table, module_index, "")
        
        page_layout.addWidget(table)
        
        return page
    
    def _populate_table_data(self, table: QTableWidget, module_index: int, search_text: str = "") -> None:
        """填充表格数据"""
        try:
            # 获取当前视图模式
            current_mode = self.current_view_mode.get(module_index, 'all')
            
            # 币圈模块使用真实数据
            if module_index == 1 and hasattr(self, 'crypto_data') and self.crypto_data:
                # 使用虚拟币真实数据
                data = []
                for crypto in self.crypto_data:
                    symbol = f"{crypto['baseAsset']}/{crypto['quoteAsset']}"
                    symbol_pair = f"{crypto['baseAsset']}USDT"
                    price = f"{float(crypto['price']):.4f}"
                    change_percent = float(crypto['change_percent'])
                    change = f"{change_percent:+.2f}%"
                    
                    # 应用自选过滤
                    if current_mode == 'favorite' and symbol_pair not in self.favorite_symbols:
                        continue
                    
                    # 应用搜索过滤
                    if search_text:
                        search_upper = search_text.upper()
                        # 检查多种匹配方式
                        symbol_upper = symbol.upper()
                        base_asset = crypto['baseAsset'].upper()
                        quote_asset = crypto['quoteAsset'].upper()
                        symbol_pair_upper = symbol_pair.upper()
                        
                        # 如果搜索文本不匹配任何字段，则跳过
                        if not any([
                            search_upper in symbol_upper,
                            search_upper in base_asset,
                            search_upper in quote_asset,
                            search_upper in symbol_pair_upper,
                            base_asset.startswith(search_upper),
                            symbol_pair_upper.startswith(search_upper)
                        ]):
                            continue
                    
                    data.append((symbol, price, change))
            else:
                # 其他模块使用示例数据
                sample_data = [
                    # 商品数据
                    [
                        ("美元指数", "97.833", "+0.27%"),
                        ("黄金", "1,985.50", "-0.15%"),
                        ("原油", "78.45", "+1.23%"),
                        ("天然气", "2.856", "-2.45%"),
                        ("铜", "8,234.50", "+0.89%"),
                        ("白银", "24.67", "-0.34%"),
                        ("铂金", "1,045.20", "+0.67%"),
                        ("钯金", "1,567.80", "-1.12%")
                    ],
                    # 币圈数据
                    [
                        ("BTC/USDT", "43,256.78", "+2.34%"),
                        ("ETH/USDT", "2,567.89", "+1.87%"),
                        ("BNB/USDT", "312.45", "+0.95%"),
                        ("ADA/USDT", "0.4567", "-1.23%"),
                        ("SOL/USDT", "87.4048", "-2.76%"),
                        ("XRP/USDT", "0.6234", "+3.45%"),
                        ("DOT/USDT", "7.89", "-0.67%"),
                        ("AVAX/USDT", "23.45", "+1.56%")
                    ],
                    # 外汇数据
                    [
                        ("EUR/USD", "1.0876", "+0.12%"),
                        ("GBP/USD", "1.2634", "-0.23%"),
                        ("USD/JPY", "149.87", "+0.45%"),
                        ("AUD/USD", "0.6789", "-0.34%"),
                        ("USD/CAD", "1.3456", "+0.18%"),
                        ("USD/CHF", "0.8934", "-0.12%"),
                        ("NZD/USD", "0.6123", "+0.28%"),
                        ("EUR/GBP", "0.8612", "+0.09%")
                    ]
                ]
                
                raw_data = sample_data[module_index] if module_index < len(sample_data) else []
                
                # 应用自选过滤
                if current_mode == 'favorite':
                    # 为其他模块定义一些示例自选品种
                    favorite_items = {
                        0: ["美元指数", "黄金", "原油"],  # 商品
                        2: ["EUR/USD", "GBP/USD", "USD/JPY"]  # 外汇
                    }
                    if module_index in favorite_items:
                        raw_data = [item for item in raw_data if any(fav in item[0] for fav in favorite_items[module_index])]
                
                # 应用搜索过滤
                if search_text:
                    search_upper = search_text.upper()
                    data = [
                        item for item in raw_data
                        if search_upper in item[0].upper()  # 在品种名称中搜索
                    ]
                else:
                    data = raw_data
            
            table.setRowCount(len(data))
            
            for row, (symbol, price, change) in enumerate(data):
                # 品种/代码
                symbol_item = QTableWidgetItem(symbol)
                symbol_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                table.setItem(row, 0, symbol_item)
                
                # 价格
                price_item = QTableWidgetItem(price)
                price_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                table.setItem(row, 1, price_item)
                
                # 涨跌幅
                change_item = QTableWidgetItem(change)
                change_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                
                # 根据涨跌设置颜色
                if change.startswith("+"):
                    change_item.setForeground(Qt.GlobalColor.red)  # 上涨红色
                elif change.startswith("-"):
                    change_item.setForeground(Qt.GlobalColor.green)  # 下跌绿色
                
                table.setItem(row, 2, change_item)
                
        except Exception as e:
            self.logger.error(f"填充表格数据失败: {e}")
    
    def show_module(self, index: int, sidebar_widget=None) -> None:
        """显示指定模块，并定位到侧边栏右侧"""
        if 0 <= index < len(self.module_titles) and index < self.content_stack.count():
            self._current_index = index
            self.title_label.setText(self.module_titles[index])
            self.content_stack.setCurrentIndex(index)
            
            # 初始化视图模式为"全部"
            if index not in self.current_view_mode:
                self.current_view_mode[index] = 'all'
            
            # 更新按钮状态
            if hasattr(self, 'view_buttons') and index in self.view_buttons:
                current_mode = self.current_view_mode[index]
                self._on_view_mode_changed(index, current_mode)
            
            # 如果是币圈模块，启动数据获取
            if index == 1 and hasattr(self, 'crypto_vm') and self.crypto_vm:
                try:
                    # 总是尝试刷新数据以获取最新信息
                    self.crypto_vm.refresh_data()
                    # 立即刷新表格显示现有数据
                    self._refresh_current_table()
                    self.logger.info("已启动虚拟币数据获取")
                except Exception as e:
                    self.logger.error(f"启动虚拟币数据获取失败: {e}")
            
            # 计算弹窗位置（侧边栏右侧）- 可在此调整弹窗相对于侧边栏的位置
            if sidebar_widget and sidebar_widget.parent():
                # 更新弹窗尺寸以匹配侧边栏高度
                if hasattr(sidebar_widget, 'POPUP_HEIGHT') and sidebar_widget.POPUP_HEIGHT == -1:
                    main_window_height = sidebar_widget.parent().height()
                    self.setFixedSize(sidebar_widget.POPUP_WIDTH, main_window_height)
                
                parent_pos = sidebar_widget.parent().mapToGlobal(sidebar_widget.pos())
                popup_x = parent_pos.x() + sidebar_widget.width() + 5  # 弹窗距离侧边栏的水平距离
                popup_y = parent_pos.y()  # 与侧边栏顶部对齐
                self.move(popup_x, popup_y)
            
            self.show()
            self.raise_()
            self.logger.info(f"显示{self.module_titles[index]}模块弹窗")
    
    def hide_popup(self) -> None:
        """隐藏弹窗"""
        self.hide()
        self._current_index = -1
        self.logger.info("隐藏侧边栏弹窗")
    
    def cleanup(self) -> None:
        """清理资源"""
        try:
            if hasattr(self, 'crypto_vm') and self.crypto_vm:
                self.crypto_vm.disconnect()
                self.crypto_vm = None
            if hasattr(self, 'crypto_data'):
                self.crypto_data.clear()
        except Exception as e:
            self.logger.error(f"清理资源失败: {e}")
    
    def is_popup_visible(self) -> bool:
        """检查弹窗是否可见"""
        return self.isVisible()
    
    def get_current_index(self) -> int:
        """获取当前显示的模块索引"""
        return self._current_index
    
    def _create_history_page(self) -> QWidget:
        """创建历史数据页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 标题
        title_label = QLabel("📚 历史数据管理")
        title_label.setStyleSheet("""
            QLabel {
                color: #ecf0f1;
                font-size: 16px;
                font-weight: bold;
                padding: 5px 0;
                border-bottom: 1px solid #3498db;
            }
        """)
        layout.addWidget(title_label)
        
        # 刷新按钮
        refresh_btn = QPushButton("🔄 刷新历史数据")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        refresh_btn.clicked.connect(self._refresh_history_data)
        layout.addWidget(refresh_btn)
        
        # 历史数据表格
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(4)
        self.history_table.setHorizontalHeaderLabels(["品种", "时间周期", "年份", "操作"])
        
        # 设置表格样式
        self.history_table.setStyleSheet("""
            QTableWidget {
                background-color: rgba(44, 62, 80, 180);
                border: 1px solid #34495e;
                border-radius: 5px;
                gridline-color: #34495e;
                color: #ecf0f1;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #34495e;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
            }
            QHeaderView::section {
                background-color: #2c3e50;
                color: #ecf0f1;
                padding: 8px;
                border: 1px solid #34495e;
                font-weight: bold;
                font-size: 11px;
            }
        """)
        
        # 设置表格属性
        header = self.history_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        
        self.history_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.history_table.setAlternatingRowColors(True)
        
        layout.addWidget(self.history_table)
        
        # 初始化历史数据
        self._refresh_history_data()
        
        return page
    
    def _refresh_history_data(self) -> None:
        """刷新历史数据列表"""
        try:
            import os
            import glob
            
            # 历史数据目录
            history_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'bars_yearly')
            
            if not os.path.exists(history_dir):
                self.logger.warning(f"历史数据目录不存在: {history_dir}")
                return
            
            # 获取所有parquet文件
            pattern = os.path.join(history_dir, '*.parquet')
            files = glob.glob(pattern)
            
            # 清空表格
            self.history_table.setRowCount(0)
            
            # 解析文件名并添加到表格
            for file_path in files:
                filename = os.path.basename(file_path)
                # 解析文件名格式: SYMBOL_INTERVAL_YEAR.parquet
                parts = filename.replace('.parquet', '').split('_')
                if len(parts) >= 3:
                    symbol = parts[0]
                    interval = parts[1]
                    year = parts[2]
                    
                    # 添加行
                    row = self.history_table.rowCount()
                    self.history_table.insertRow(row)
                    
                    # 设置数据
                    self.history_table.setItem(row, 0, QTableWidgetItem(symbol))
                    self.history_table.setItem(row, 1, QTableWidgetItem(interval))
                    self.history_table.setItem(row, 2, QTableWidgetItem(year))
                    
                    # 创建加载按钮
                    load_btn = QPushButton("📈 加载")
                    load_btn.setStyleSheet("""
                        QPushButton {
                            background-color: #27ae60;
                            color: white;
                            border: none;
                            padding: 4px 8px;
                            border-radius: 3px;
                            font-size: 10px;
                        }
                        QPushButton:hover {
                            background-color: #229954;
                        }
                    """)
                    
                    # 连接点击事件，传递文件路径和品种信息
                    load_btn.clicked.connect(
                        lambda checked, fp=file_path, sym=symbol: self._load_historical_data(sym, fp)
                    )
                    
                    self.history_table.setCellWidget(row, 3, load_btn)
            
            self.logger.info(f"已加载 {len(files)} 个历史数据文件")
            
        except Exception as e:
            self.logger.error(f"刷新历史数据失败: {e}")
    
    def _load_historical_data(self, symbol: str, file_path: str) -> None:
        """加载历史数据到主图"""
        try:
            self.logger.info(f"加载历史数据: {symbol} from {file_path}")
            # 发射信号，通知主窗口加载历史数据
            self.load_historical_data.emit(symbol, file_path)
            
        except Exception as e:
            self.logger.error(f"加载历史数据失败: {e}")


class Sidebar(QWidget):
    """
    左侧边栏组件
    包含模块按钮和弹窗管理
    
    可调节参数说明：
    - SIDEBAR_WIDTH: 侧边栏宽度（像素）
    - BUTTON_HEIGHT: 按钮高度（像素）
    - ICON_SIZE: 图标字体大小（像素）
    - TEXT_SIZE: 文字字体大小（像素）
    - POPUP_HEIGHT: 弹窗高度（像素）
    - POPUP_WIDTH: 弹窗宽度（像素）
    """
    
    # 可调节的UI参数 - 在这里修改可以改变外观
    SIDEBAR_WIDTH = 45      # 侧边栏宽度
    BUTTON_HEIGHT = 60      # 按钮高度
    ICON_SIZE = 15          # 图标字体大小
    TEXT_SIZE = 13          # 文字字体大小
    POPUP_HEIGHT = -1       # 弹窗高度（-1表示与侧边栏同高）
    POPUP_WIDTH = 400       # 弹窗宽度
    
    # 信号定义
    module_clicked = Signal(int)  # 模块被点击
    popup_show_requested = Signal(int, object)  # 请求显示弹窗
    popup_hide_requested = Signal()  # 请求隐藏弹窗
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        self._setup_ui()
        
        # 模块信息
        self.module_titles = ["商品", "币圈", "外汇", "指标编辑", "回测", "交易", "历史"]
        self.popup = None
        self._current_active_button = -1
    
    def _setup_ui(self) -> None:
        """设置UI界面"""
        # 设置固定宽度 - 修改 SIDEBAR_WIDTH 可以调节侧边栏宽度
        self.setFixedWidth(self.SIDEBAR_WIDTH)
        self.setStyleSheet("""
            QWidget {
                background-color: #2c3e50;
                border-right: 1px solid #34495e;
            }
        """)
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(2)
        
        # 创建按钮
        self.buttons = []
        button_configs = [
            ("商品", "📦"),
            ("币圈", "₿"),
            ("外汇", "💱"),
            ("指标编辑", "📊"),
            ("回测", "📈"),
            ("交易", "💰"),
            ("历史", "📚")
        ]
        
        for i, (name, icon) in enumerate(button_configs):
            btn = QPushButton(f"{icon}\n{name}")
            # 按钮尺寸 - 修改 BUTTON_HEIGHT 可以调节按钮高度
            btn.setFixedSize(self.SIDEBAR_WIDTH, self.BUTTON_HEIGHT)
            # 按钮样式 - 修改 TEXT_SIZE 可以调节字体大小
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: transparent;
                    color: #ecf0f1;
                    border: none;
                    font-size: {self.TEXT_SIZE}px;  /* 文字大小 - 可在类常量中调节 */
                    font-weight: bold;
                    text-align: center;
                    padding: 8px;
                    border-radius: 5px;
                }}
                QPushButton:hover {{
                    background-color: #34495e;
                    border: 1px solid #3498db;
                }}
                QPushButton:pressed {{
                    background-color: #3498db;
                }}
                QPushButton[active="true"] {{
                    background-color: #2980b9;
                    border: 2px solid #3498db;
                }}
            """)
            
            # 连接点击事件
            btn.clicked.connect(lambda checked, idx=i: self._on_button_clicked(idx))
            
            self.buttons.append(btn)
            layout.addWidget(btn)
        
        # 添加弹簧，将按钮推到顶部
        layout.addStretch()
    
    def _on_button_clicked(self, index: int) -> None:
        """处理按钮点击事件"""
        try:
            # 如果点击的是当前激活的按钮，则隐藏弹窗
            if self._current_active_button == index and self.is_popup_visible():
                self.hide_popup()
                return
            
            # 更新按钮状态
            self._update_button_states(index)
            
            # 发射信号
            self.module_clicked.emit(index)
            self.popup_show_requested.emit(index, self.module_titles)
            
            self.logger.info(f"侧边栏按钮被点击: {self.module_titles[index]}")
            
        except Exception as e:
            self.logger.error(f"处理按钮点击失败: {e}")
    
    def _update_button_states(self, active_index: int) -> None:
        """更新按钮状态"""
        for i, btn in enumerate(self.buttons):
            if i == active_index:
                btn.setProperty("active", "true")
                self._current_active_button = i
            else:
                btn.setProperty("active", "false")
            btn.style().unpolish(btn)
            btn.style().polish(btn)
    
    def clear_button_states(self) -> None:
        """清除所有按钮的激活状态"""
        for btn in self.buttons:
            btn.setProperty("active", "false")
            btn.style().unpolish(btn)
            btn.style().polish(btn)
        self._current_active_button = -1
    
    def get_current_active_button(self) -> int:
        """获取当前激活的按钮索引"""
        return self._current_active_button
    
    def set_popup_widget(self, popup: SidebarPopup) -> None:
        """设置弹窗组件"""
        self.popup = popup
        
        # 连接信号
        self.popup_show_requested.connect(self._show_popup)
        self.popup_hide_requested.connect(self._hide_popup)
    
    def _show_popup(self, index: int, titles: List[str]) -> None:
        """显示弹窗"""
        if self.popup:
            self.popup.show_module(index, self)
    
    def _hide_popup(self) -> None:
        """隐藏弹窗"""
        if self.popup:
            self.popup.hide_popup()
        self.clear_button_states()
    
    def hide_popup(self) -> None:
        """外部调用隐藏弹窗"""
        self.popup_hide_requested.emit()
    
    def is_popup_visible(self) -> bool:
        """检查弹窗是否可见"""
        return self.popup and self.popup.is_popup_visible()