#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试交易悬浮窗功能
验证商品、币圈、外汇模块的新界面
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from PySide6.QtWidgets import QApplication, QMainWindow, QHBoxLayout, QWidget
    from PySide6.QtCore import Qt, QTimer
    from gui.widgets.sidebar import Sidebar, SidebarPopup
    GUI_AVAILABLE = True
except ImportError as e:
    print(f"GUI库导入失败: {e}")
    GUI_AVAILABLE = False
    sys.exit(1)


class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("交易悬浮窗测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建侧边栏
        self.sidebar = Sidebar(self)
        main_layout.addWidget(self.sidebar)
        
        # 创建弹窗
        self.popup = SidebarPopup(self)
        self.sidebar.set_popup_widget(self.popup)
        
        # 连接信号
        self.sidebar.module_clicked.connect(self.on_module_clicked)
        
        # 添加主内容区域（占位）
        content_widget = QWidget()
        content_widget.setStyleSheet("""
            QWidget {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
            }
        """)
        main_layout.addWidget(content_widget, 1)
        
        print("测试窗口初始化完成")
        print("点击左侧边栏的'商品'、'币圈'、'外汇'按钮查看新的悬浮窗界面")
    
    def on_module_clicked(self, index: int):
        """处理模块点击事件"""
        module_names = ["商品", "币圈", "外汇", "指标编辑", "回测", "交易"]
        if index < len(module_names):
            print(f"点击了{module_names[index]}模块")
            if index < 3:  # 前三个模块有新的界面
                print(f"显示{module_names[index]}的交易数据界面")
    
    def mousePressEvent(self, event):
        """点击主界面隐藏弹窗"""
        if self.popup.is_popup_visible():
            # 检查点击位置是否在弹窗外
            popup_rect = self.popup.geometry()
            click_pos = self.mapToGlobal(event.pos())
            
            if not popup_rect.contains(click_pos):
                self.sidebar.hide_popup()
        
        super().mousePressEvent(event)


def test_trading_popup():
    """测试交易悬浮窗功能"""
    if not GUI_AVAILABLE:
        print("GUI库不可用，跳过测试")
        return False
    
    try:
        app = QApplication(sys.argv)
        
        # 创建测试窗口
        window = TestMainWindow()
        window.show()
        
        print("\n=== 交易悬浮窗测试 ===")
        print("1. 点击'商品'按钮 - 查看商品交易数据")
        print("2. 点击'币圈'按钮 - 查看加密货币数据")
        print("3. 点击'外汇'按钮 - 查看外汇交易数据")
        print("4. 测试搜索功能和分类切换")
        print("5. 点击主界面空白处关闭弹窗")
        print("6. 关闭窗口结束测试")
        
        # 自动测试序列
        def auto_test():
            print("\n开始自动测试...")
            
            # 测试商品模块
            print("测试商品模块...")
            window.sidebar.buttons[0].click()
            
            QTimer.singleShot(2000, lambda: (
                print("测试币圈模块（包含真实虚拟币数据）..."),
                window.sidebar.buttons[1].click()
            ))
            
            # 等待更长时间让虚拟币数据加载
            QTimer.singleShot(7000, lambda: (
                print("测试外汇模块..."),
                window.sidebar.buttons[2].click()
            ))
            
            QTimer.singleShot(9000, lambda: (
                print("隐藏弹窗..."),
                window.sidebar.hide_popup()
            ))
            
            QTimer.singleShot(11000, lambda: (
                print("自动测试完成！"),
                print("现在可以手动测试各项功能"),
                print("- 币圈模块现在显示真实的虚拟币数据")
            ))
        
        # 延迟启动自动测试
        QTimer.singleShot(1000, auto_test)
        
        # 运行应用
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    test_trading_popup()