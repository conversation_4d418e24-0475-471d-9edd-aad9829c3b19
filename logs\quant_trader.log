2025-07-11 07:51:16 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 07:51:16 - main_controller - [32m<PERSON><PERSON><PERSON>[0m - 事件总线初始化完成
2025-07-11 07:51:16 - main_controller - [32m<PERSON><PERSON><PERSON>[0m - 启动模块: infrastructure
2025-07-11 07:51:16 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 07:51:16 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 07:51:16 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 07:51:16 - main_controller - [32mINF<PERSON>[0m - 启动模块: strategy_engine
2025-07-11 07:51:16 - main_controller - [32mINF<PERSON>[0m - 模块 strategy_engine 启动成功
2025-07-11 07:51:16 - main_controller - [32mINF<PERSON>[0m - 启动模块: execution_portfolio
2025-07-11 07:51:16 - main_controller - [32mINF<PERSON>[0m - 模块 execution_portfolio 启动成功
2025-07-11 07:51:16 - main_controller - [32mINF<PERSON>[0m - 启动模块: gui
2025-07-11 07:51:16 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 07:51:16 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 07:51:16 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 07:51:29 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 08:14:44 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 08:14:44 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 08:14:44 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 08:14:44 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 08:14:44 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 08:14:51 - main_controller - [31mERROR[0m - 模块 data_service 启动失败: cannot pickle '_thread.lock' object
2025-07-11 08:14:51 - main_controller - [31mERROR[0m - 系统启动失败: cannot pickle '_thread.lock' object
2025-07-11 08:20:14 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 08:20:14 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 08:20:14 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 08:20:14 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 08:20:14 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 08:20:14 - main_controller - [31mERROR[0m - 模块 data_service 启动失败: cannot pickle '_thread.lock' object
2025-07-11 08:20:14 - main_controller - [31mERROR[0m - 系统启动失败: cannot pickle '_thread.lock' object
2025-07-11 08:38:35 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 08:38:35 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 08:38:35 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 08:38:35 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 08:38:35 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 08:38:36 - main_controller - [31mERROR[0m - 模块 data_service 启动失败: cannot pickle '_thread.lock' object
2025-07-11 08:38:36 - main_controller - [31mERROR[0m - 系统启动失败: cannot pickle '_thread.lock' object
2025-07-11 08:38:36 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 08:38:36 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 08:38:36 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 08:38:36 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 08:38:36 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 08:38:37 - main_controller - [31mERROR[0m - 模块 data_service 启动失败: cannot pickle '_thread.lock' object
2025-07-11 08:38:37 - main_controller - [31mERROR[0m - 系统启动失败: cannot pickle '_thread.lock' object
2025-07-11 08:41:43 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 08:41:43 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 08:41:43 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 08:41:43 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 08:41:43 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 08:41:44 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 08:41:44 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 08:41:44 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 08:41:44 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 08:41:44 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 08:41:44 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 08:41:44 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 08:41:44 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 08:41:44 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 08:42:29 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 08:43:26 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 08:43:26 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 08:43:26 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 08:43:26 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 08:43:26 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 08:43:27 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 08:43:27 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 08:43:27 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 08:43:27 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 08:43:27 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 08:43:27 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 08:43:27 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 08:43:27 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 08:43:27 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 08:43:42 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 08:52:21 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 08:52:21 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 08:52:21 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 08:52:21 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 08:52:21 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 08:52:22 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 08:52:22 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 08:52:22 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 08:52:22 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 08:52:22 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 08:52:22 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 08:52:22 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 08:52:22 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 08:52:22 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 08:52:24 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-11 08:52:24 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-11 08:52:24 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-11 08:52:24 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-11 08:52:24 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-11 08:52:24 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-11 08:52:24 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-11 08:52:24 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-11 08:52:24 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-11 08:52:24 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-11 08:52:24 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-11 08:52:24 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-11 09:44:43 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 09:44:43 - main_controller - [31mERROR[0m - 通知服务初始化失败: 'ConfigManager' object has no attribute 'get_config'
2025-07-11 09:44:43 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 09:44:43 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 09:44:43 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 09:44:43 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 09:44:44 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 09:44:44 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 09:44:45 - main_controller - [31mERROR[0m - 模块 strategy_engine 启动失败: cannot import name 'Backtester' from 'strategy_ai_engine.backtester' (E:\Quant_traders\Gemini_quant\strategy_ai_engine\backtester.py)
2025-07-11 09:44:45 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'Backtester' from 'strategy_ai_engine.backtester' (E:\Quant_traders\Gemini_quant\strategy_ai_engine\backtester.py)
2025-07-11 09:47:10 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 09:47:10 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 09:47:10 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 09:47:10 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 09:47:10 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 09:47:10 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 09:47:10 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 09:47:10 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 09:47:10 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 09:47:10 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 09:47:10 - main_controller - [31mERROR[0m - 模块 execution_portfolio 启动失败: cannot import name 'OrderRequestEvent' from 'core.event_types' (E:\Quant_traders\Gemini_quant\core\event_types.py)
2025-07-11 09:47:10 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'OrderRequestEvent' from 'core.event_types' (E:\Quant_traders\Gemini_quant\core\event_types.py)
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 09:48:03 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 09:49:29 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 09:49:42 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 11:03:05 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:03:05 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:03:05 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:03:05 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:03:05 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:03:05 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:03:06 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:03:06 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:03:06 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:03:06 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:03:06 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:03:06 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:03:06 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 11:03:06 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 11:03:06 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 11:05:17 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:06:33 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 11:07:33 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 11:10:33 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:10:35 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:10:36 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:10:36 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:11:13 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:11:13 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:11:13 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:11:13 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:11:13 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:11:13 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:11:13 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:11:13 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:11:14 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:11:14 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:11:14 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:11:14 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:11:14 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 11:11:14 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 11:11:14 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 11:12:41 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:15:41 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:28 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:29 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:30 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:30 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:30 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:31 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:31 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:31 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:31 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:31 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:31 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:31 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:31 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:21:31 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:23:36 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:23:36 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:23:36 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:23:36 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:23:36 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:23:36 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:23:36 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:23:36 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:23:36 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:23:36 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:23:36 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:23:36 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:23:37 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 11:23:37 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 11:23:37 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 11:34:39 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:34:51 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:34:51 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:34:52 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:35:54 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:35:54 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:36:53 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:36:53 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:36:53 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:36:53 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:36:53 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:36:53 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:36:54 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:36:54 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:36:54 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:36:54 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:36:54 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:36:54 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:36:54 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 11:36:54 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 11:36:54 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 11:41:12 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-11 11:41:57 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:41:57 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:41:57 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:41:57 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:41:57 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:41:57 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:41:57 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:41:57 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:41:57 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:41:57 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:41:57 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:41:57 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:47:21 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:47:21 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:47:21 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:47:21 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:47:21 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:47:21 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:47:22 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:47:22 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:47:22 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:47:22 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:47:22 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:47:22 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:50:42 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:50:42 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:50:42 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:50:42 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:50:42 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:50:42 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:50:42 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:50:42 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:50:42 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:50:42 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:50:42 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:50:42 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:50:43 - main_controller - [31mERROR[0m - 模块 gui 启动失败: argument should be a str or an os.PathLike object where __fspath__ returns a str, not 'dict'
2025-07-11 11:50:43 - main_controller - [31mERROR[0m - 系统启动失败: argument should be a str or an os.PathLike object where __fspath__ returns a str, not 'dict'
2025-07-11 11:51:31 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:51:31 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:51:31 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:51:31 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:51:31 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:51:31 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:51:32 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:51:32 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:51:32 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:51:32 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:51:32 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:51:32 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:51:32 - main_controller - [31mERROR[0m - 模块 gui 启动失败: argument should be a str or an os.PathLike object where __fspath__ returns a str, not 'dict'
2025-07-11 11:51:32 - main_controller - [31mERROR[0m - 系统启动失败: argument should be a str or an os.PathLike object where __fspath__ returns a str, not 'dict'
2025-07-11 11:52:29 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:52:29 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:52:29 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:52:29 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:52:29 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:52:29 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:52:29 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:52:29 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:52:29 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:52:29 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:52:29 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:52:29 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:52:30 - main_controller - [31mERROR[0m - 模块 gui 启动失败: argument should be a str or an os.PathLike object where __fspath__ returns a str, not 'dict'
2025-07-11 11:52:30 - main_controller - [31mERROR[0m - 系统启动失败: argument should be a str or an os.PathLike object where __fspath__ returns a str, not 'dict'
2025-07-11 11:52:52 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:52:52 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:52:52 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:52:52 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:52:52 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:52:52 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:52:52 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:52:52 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:52:52 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:52:52 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:52:52 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:52:52 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:52:53 - main_controller - [31mERROR[0m - 模块 gui 启动失败: argument should be a str or an os.PathLike object where __fspath__ returns a str, not 'dict'
2025-07-11 11:52:53 - main_controller - [31mERROR[0m - 系统启动失败: argument should be a str or an os.PathLike object where __fspath__ returns a str, not 'dict'
2025-07-11 11:53:37 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:53:37 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:53:37 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:53:37 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:53:37 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:53:37 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:53:37 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:53:37 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:53:37 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:53:37 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:53:37 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:53:37 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:53:38 - main_controller - [31mERROR[0m - 模块 gui 启动失败: 'PySide6.QtWidgets.QTextEdit' object has no attribute 'setMaximumBlockCount'
2025-07-11 11:53:38 - main_controller - [31mERROR[0m - 系统启动失败: 'PySide6.QtWidgets.QTextEdit' object has no attribute 'setMaximumBlockCount'
2025-07-11 11:54:10 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:54:10 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:54:10 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:54:10 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:54:10 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:54:10 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:54:10 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:54:10 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:54:10 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:54:10 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:54:10 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:54:10 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:54:11 - main_controller - [31mERROR[0m - 模块 gui 启动失败: 'PySide6.QtWidgets.QTextEdit' object has no attribute 'setMaximumBlockCount'
2025-07-11 11:54:11 - main_controller - [31mERROR[0m - 系统启动失败: 'PySide6.QtWidgets.QTextEdit' object has no attribute 'setMaximumBlockCount'
2025-07-11 11:55:01 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:55:01 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:55:01 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:55:01 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:55:01 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:55:01 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:55:01 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:55:01 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:55:02 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:55:02 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:55:02 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:55:02 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-11 11:55:03 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-11 11:55:55 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:55:55 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:55:55 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:55:55 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:55:55 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:55:55 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:55:57 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:55:57 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:55:57 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:55:57 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:55:57 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:55:57 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-11 11:55:58 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-11 11:56:54 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:56:54 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:56:54 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:56:54 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:56:54 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:56:54 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:56:57 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:56:57 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:56:58 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:56:58 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:56:58 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:56:58 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:56:59 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 11:56:59 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 11:56:59 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 11:56:59 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-11 11:56:59 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-11 11:56:59 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-11 11:56:59 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-11 11:56:59 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-11 11:56:59 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-11 11:56:59 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-11 11:56:59 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-11 11:57:00 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-11 11:57:00 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-11 11:57:00 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-11 11:57:00 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-11 11:58:22 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 11:58:22 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 11:58:22 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 11:58:22 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 11:58:22 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 11:58:22 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 11:58:23 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 11:58:23 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 11:58:23 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 11:58:23 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 11:58:23 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 11:58:23 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 进入主循环
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-11 11:58:24 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-11 12:05:38 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 12:05:38 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 12:05:38 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 12:05:38 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 12:05:38 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 12:05:38 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 12:05:38 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 12:05:38 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 12:05:38 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 12:05:38 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 12:05:39 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 12:05:39 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 12:05:39 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 12:05:39 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 12:06:17 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 12:06:17 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 12:06:17 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 12:06:17 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 12:06:17 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 12:06:17 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 12:06:17 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 12:06:17 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 12:06:18 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 12:06:18 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 12:06:18 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 12:06:18 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 12:06:18 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 12:06:18 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 12:06:46 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-11 12:06:46 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-11 12:06:46 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-11 12:06:46 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-11 12:06:46 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-11 12:06:46 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-11 12:06:46 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-11 12:06:46 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-11 12:06:46 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-11 12:06:46 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-11 12:06:46 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-11 12:06:46 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-11 14:11:27 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 14:11:27 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 14:11:27 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 14:11:27 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 14:11:27 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 14:11:27 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 14:11:28 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 14:11:28 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 14:11:28 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 14:11:28 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 14:11:28 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 14:11:28 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 14:11:29 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 14:11:29 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 14:13:19 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-11 14:13:19 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-11 14:13:19 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-11 14:13:19 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-11 14:13:19 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-11 14:13:19 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-11 14:13:19 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-11 14:13:19 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-11 14:13:19 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-11 14:13:19 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-11 14:13:19 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-11 14:13:19 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-11 14:47:20 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 14:47:20 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 14:47:20 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 14:47:20 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 14:47:20 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 14:47:20 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 14:47:21 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 14:47:21 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 14:47:21 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 14:47:21 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 14:47:21 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 14:47:21 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 14:47:22 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 14:47:22 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 15:08:36 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-11 15:08:36 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-11 15:08:36 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-11 15:08:36 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-11 15:08:36 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-11 15:08:36 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-11 15:08:36 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-11 15:08:36 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-11 15:08:36 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-11 15:08:36 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-11 15:08:36 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-11 15:08:36 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-11 19:07:53 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 19:07:53 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 19:07:53 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 19:07:53 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 19:07:53 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 19:07:53 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 19:07:53 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 19:07:53 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 19:07:53 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 19:07:53 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 19:07:53 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 19:07:53 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 19:07:54 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-11 19:07:54 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-11 21:25:29 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-11 21:25:29 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-11 21:25:29 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-11 21:25:29 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-11 21:25:29 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-11 21:25:29 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-11 21:25:29 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-11 21:25:29 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-11 21:25:29 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-11 21:25:29 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-11 21:25:29 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-11 21:25:29 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-11 21:25:38 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 21:25:38 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 21:25:38 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 21:25:38 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 21:25:38 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 21:25:38 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 21:25:38 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 21:25:38 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 21:25:38 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 21:25:38 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 21:25:38 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 21:25:38 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 21:25:39 - main_controller - [31mERROR[0m - 模块 gui 启动失败: 'MainWindow' object has no attribute '_show_crypto_list'
2025-07-11 21:25:39 - main_controller - [31mERROR[0m - 系统启动失败: 'MainWindow' object has no attribute '_show_crypto_list'
2025-07-11 21:30:54 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-11 21:30:54 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-11 21:30:54 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-11 21:30:54 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-11 21:30:54 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-11 21:30:54 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-11 21:30:55 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-11 21:30:55 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-11 21:30:55 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-11 21:30:55 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-11 21:30:55 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-11 21:30:55 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-11 21:30:55 - main_controller - [31mERROR[0m - 模块 gui 启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-11 21:30:55 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 07:39:43 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 07:39:43 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 07:39:43 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 07:39:43 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 07:39:43 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 07:39:43 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 07:39:44 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 07:39:44 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 07:39:44 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 07:39:44 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 07:39:44 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 07:39:44 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 07:39:44 - main_controller - [31mERROR[0m - 模块 gui 启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 07:39:44 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 08:01:10 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 08:01:10 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 08:01:10 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 08:01:10 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 08:01:10 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 08:01:10 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 08:01:10 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 08:01:10 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 08:01:10 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 08:01:10 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 08:01:10 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 08:01:10 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 08:01:11 - main_controller - [31mERROR[0m - 模块 gui 启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 08:01:11 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 09:43:49 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 09:43:49 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 09:43:49 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 09:43:49 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 09:43:49 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 09:43:49 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 09:43:50 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 09:43:50 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 09:43:50 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 09:43:50 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 09:43:50 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 09:43:50 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 09:43:50 - main_controller - [31mERROR[0m - 模块 gui 启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 09:43:50 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 09:46:03 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 09:46:03 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 09:46:03 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 09:46:03 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 09:46:03 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 09:46:03 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 09:46:04 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 09:46:04 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 09:46:04 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 09:46:04 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 09:46:04 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 09:46:04 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 09:46:04 - main_controller - [31mERROR[0m - 模块 gui 启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 09:46:04 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 10:10:25 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 10:10:25 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 10:10:25 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 10:10:25 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 10:10:25 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 10:10:25 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 10:10:26 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 10:10:26 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 10:10:26 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 10:10:26 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 10:10:26 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 10:10:26 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 10:10:26 - main_controller - [31mERROR[0m - 模块 gui 启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 10:10:26 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 10:12:34 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 10:12:34 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 10:12:34 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 10:12:34 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 10:12:34 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 10:12:34 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 10:12:35 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 10:12:35 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 10:12:35 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 10:12:35 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 10:12:35 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 10:12:35 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 10:12:35 - main_controller - [31mERROR[0m - 模块 gui 启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 10:12:35 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 10:12:48 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 10:12:48 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 10:12:48 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 10:12:48 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 10:12:48 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 10:12:48 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 10:12:49 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 10:12:49 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 10:12:49 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 10:12:49 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 10:12:49 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 10:12:49 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 10:12:49 - main_controller - [31mERROR[0m - 模块 gui 启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 10:12:49 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 11:31:42 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 11:31:42 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 11:31:42 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 11:31:42 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 11:31:42 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 11:31:42 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 11:31:43 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 11:31:43 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 11:31:43 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 11:31:43 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 11:31:43 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 11:31:43 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 11:31:43 - main_controller - [31mERROR[0m - 模块 gui 启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 11:31:43 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 11:34:19 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 11:34:19 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 11:34:19 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 11:34:19 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 11:34:19 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 11:34:19 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 11:34:20 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 11:34:20 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 11:34:20 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 11:34:20 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 11:34:20 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 11:34:20 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 11:34:20 - main_controller - [31mERROR[0m - 模块 gui 启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 11:34:20 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 11:36:59 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 11:36:59 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 11:36:59 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 11:36:59 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 11:36:59 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 11:36:59 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 11:37:00 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 11:37:00 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 11:37:00 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 11:37:00 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 11:37:00 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 11:37:00 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 11:37:00 - main_controller - [31mERROR[0m - 模块 gui 启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 11:37:00 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 11:38:26 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 11:38:26 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 11:38:26 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 11:38:26 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 11:38:26 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 11:38:26 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 11:38:28 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 11:38:28 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 11:38:28 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 11:38:28 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 11:38:28 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 11:38:28 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 11:38:28 - main_controller - [32mINFO[0m - PySide6不可用，跳过GUI模块
2025-07-12 11:38:28 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-12 11:38:28 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-12 11:39:01 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:40:07 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:40:08 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:40:08 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:40:08 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:40:08 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:40:09 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:40:09 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:40:09 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:40:09 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:40:09 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:40:09 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:40:09 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:43:21 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:44:35 - main_controller - [32mINFO[0m - 收到信号 2
2025-07-12 11:44:55 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 11:44:55 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 11:44:55 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 11:44:55 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 11:44:55 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 11:44:55 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 11:44:55 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 11:44:55 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 11:44:55 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 11:44:55 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 11:44:55 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 11:44:55 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 11:44:56 - main_controller - [31mERROR[0m - 模块 gui 启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 11:44:56 - main_controller - [31mERROR[0m - 系统启动失败: cannot import name 'pyqtSignal' from 'PySide6.QtCore' (E:\Quant_traders\Gemini_quant\.venv\Lib\site-packages\PySide6\QtCore.pyd)
2025-07-12 11:45:37 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 11:45:37 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 11:45:37 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 11:45:37 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 11:45:37 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 11:45:37 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 11:45:38 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 11:45:38 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 11:45:38 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 11:45:38 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 11:45:38 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 11:45:38 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 11:45:39 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-12 11:45:39 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-12 11:45:52 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-12 11:45:52 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-12 11:45:52 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-12 11:45:52 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-12 11:45:52 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-12 11:45:52 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-12 11:45:52 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-12 11:45:52 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-12 11:45:52 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-12 11:45:52 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-12 11:45:52 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-12 11:45:52 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-12 11:49:51 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 11:49:51 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 11:49:51 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 11:49:51 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 11:49:51 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 11:49:51 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 11:49:54 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 11:49:54 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 11:49:54 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 11:49:54 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 11:49:54 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 11:49:54 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 11:49:58 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-12 11:49:58 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-12 11:51:24 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-12 11:51:24 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-12 11:51:24 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-12 11:51:24 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-12 11:51:24 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-12 11:51:24 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-12 11:51:24 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-12 11:51:24 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-12 11:51:24 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-12 11:51:24 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-12 11:51:24 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-12 11:51:24 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-12 11:52:32 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 11:52:32 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 11:52:32 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 11:52:32 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 11:52:32 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 11:52:32 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 11:52:34 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 11:52:34 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 11:52:34 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 11:52:34 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 11:52:34 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 11:52:34 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 11:52:38 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-12 11:52:38 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-12 13:17:31 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-12 13:17:31 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-12 13:17:31 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-12 13:17:31 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-12 13:17:31 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-12 13:17:31 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-12 13:17:31 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-12 13:17:31 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-12 13:17:31 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-12 13:17:31 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-12 13:17:31 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-12 13:17:31 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-12 15:28:04 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 15:28:04 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 15:28:04 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 15:28:04 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 15:28:04 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 15:28:04 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 15:28:05 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 15:28:05 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 15:28:05 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 15:28:05 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 15:28:05 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 15:28:05 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 15:28:10 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-12 15:28:10 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-12 16:15:06 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-12 16:15:06 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-12 16:15:06 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-12 16:15:06 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-12 16:15:06 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-12 16:15:06 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-12 16:15:06 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-12 16:15:06 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-12 16:15:06 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-12 16:15:06 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-12 16:15:06 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-12 16:15:06 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-12 16:19:13 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 16:19:13 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 16:19:13 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 16:19:13 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 16:19:13 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 16:19:13 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 16:19:14 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 16:19:14 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 16:19:14 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 16:19:14 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 16:19:15 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 16:19:15 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 16:19:18 - main_controller - [31mERROR[0m - 模块 gui 启动失败: name 'QPushButton' is not defined
2025-07-12 16:19:18 - main_controller - [31mERROR[0m - 系统启动失败: name 'QPushButton' is not defined
2025-07-12 16:20:13 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 16:20:13 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 16:20:13 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 16:20:13 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 16:20:13 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 16:20:13 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 16:20:15 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 16:20:15 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 16:20:15 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 16:20:15 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 16:20:15 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 16:20:15 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 16:20:18 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-12 16:20:18 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-12 16:20:46 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-12 16:20:46 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-12 16:20:46 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-12 16:20:46 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-12 16:20:46 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-12 16:20:46 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-12 16:20:46 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-12 16:20:46 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-12 16:20:46 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-12 16:20:46 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-12 16:20:46 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-12 16:20:46 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-12 16:21:41 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 16:21:41 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 16:21:41 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 16:21:41 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 16:21:41 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 16:21:41 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 16:21:43 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 16:21:43 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 16:21:43 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 16:21:43 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 16:21:43 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 16:21:43 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 16:21:46 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-12 16:21:46 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-12 16:22:01 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-12 16:22:01 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-12 16:22:01 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-12 16:22:01 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-12 16:22:01 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-12 16:22:01 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-12 16:22:01 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-12 16:22:01 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-12 16:22:01 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-12 16:22:01 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-12 16:22:01 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-12 16:22:01 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-12 16:22:41 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 16:22:41 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 16:22:41 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 16:22:41 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 16:22:41 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 16:22:41 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 16:22:43 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 16:22:43 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 16:22:43 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 16:22:43 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 16:22:43 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 16:22:43 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 16:22:47 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-12 16:22:47 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-12 16:23:16 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-12 16:23:16 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-12 16:23:16 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-12 16:23:16 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-12 16:23:16 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-12 16:23:16 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-12 16:23:16 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-12 16:23:16 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-12 16:23:16 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-12 16:23:16 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-12 16:23:16 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-12 16:23:16 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-12 16:24:07 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 16:24:07 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 16:24:07 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 16:24:07 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 16:24:07 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 16:24:07 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 16:24:09 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 16:24:09 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 16:24:09 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 16:24:09 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 16:24:09 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 16:24:09 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 16:24:13 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-12 16:24:13 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-12 17:00:08 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-12 17:00:08 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-12 17:00:08 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-12 17:00:08 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-12 17:00:08 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-12 17:00:08 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-12 17:00:08 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-12 17:00:08 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-12 17:00:08 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-12 17:00:08 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-12 17:00:08 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-12 17:00:08 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-12 19:38:51 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 19:38:51 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 19:38:51 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 19:38:51 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 19:38:51 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 19:38:51 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 19:38:52 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 19:38:52 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 19:38:53 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 19:38:53 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 19:38:53 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 19:38:53 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 19:38:57 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-12 19:38:57 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-12 19:39:43 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 19:39:43 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 19:39:43 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 19:39:43 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 19:39:43 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 19:39:43 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 19:39:45 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 19:39:45 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 19:39:45 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 19:39:45 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 19:39:45 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 19:39:45 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 19:39:49 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-12 19:39:49 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-12 19:40:24 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 19:40:24 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 19:40:24 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 19:40:24 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 19:40:24 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 19:40:24 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 19:40:25 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 19:40:25 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 19:40:26 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 19:40:26 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 19:40:26 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 19:40:26 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 19:40:30 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-12 19:40:30 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-12 19:42:50 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-12 19:42:50 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-12 19:42:50 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-12 19:42:50 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-12 19:42:50 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-12 19:42:50 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-12 19:42:50 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-12 19:42:50 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-12 19:42:50 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-12 19:42:50 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-12 19:42:50 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-12 19:42:50 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-12 19:43:01 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-12 19:43:01 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-12 19:43:01 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-12 19:43:01 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-12 19:43:01 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-12 19:43:01 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-12 19:43:03 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-12 19:43:03 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-12 19:43:03 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-12 19:43:03 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-12 19:43:03 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-12 19:43:03 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-12 19:43:07 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-12 19:43:07 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 09:17:27 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 09:17:27 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 09:17:27 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 09:17:27 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 09:17:27 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 09:17:27 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 09:17:29 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 09:17:29 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 09:17:30 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 09:17:30 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 09:17:30 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 09:17:30 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 09:17:34 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 09:17:34 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 09:27:06 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 09:27:06 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 09:27:06 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 09:27:06 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 09:27:06 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 09:27:06 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 09:27:06 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 09:27:06 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 09:27:07 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 09:27:07 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 09:27:07 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 09:27:07 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 09:33:43 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 09:33:43 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 09:33:43 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 09:33:43 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 09:33:43 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 09:33:43 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 09:33:45 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 09:33:47 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 09:33:47 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 09:33:47 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 09:33:47 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 09:33:47 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 09:33:52 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 09:33:52 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 09:54:15 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 09:54:15 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 09:54:15 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 09:54:15 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 09:54:15 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 09:54:15 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 09:54:15 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 09:54:15 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 09:54:15 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 09:54:15 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 09:54:15 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 09:54:15 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 10:15:45 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 10:15:45 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 10:15:45 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 10:15:45 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 10:15:45 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 10:15:45 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 10:15:47 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 10:15:47 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 10:15:47 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 10:15:47 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 10:15:47 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 10:15:47 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 10:15:51 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 10:15:51 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 10:27:13 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 10:27:13 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 10:27:13 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 10:27:13 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 10:27:13 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 10:27:13 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 10:27:13 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 10:27:13 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 10:27:13 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 10:27:13 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 10:27:13 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 10:27:13 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 10:38:31 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 10:38:31 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 10:38:31 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 10:38:31 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 10:38:31 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 10:38:31 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 10:38:33 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 10:38:33 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 10:38:33 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 10:38:33 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 10:38:33 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 10:38:33 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 10:38:37 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 10:38:37 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 10:39:54 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 10:39:54 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 10:39:54 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 10:39:54 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 10:39:54 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 10:39:54 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 10:39:54 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 10:39:54 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 10:39:54 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 10:39:54 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 10:39:54 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 10:39:54 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 10:40:17 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 10:40:17 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 10:40:17 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 10:40:17 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 10:40:17 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 10:40:17 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 10:40:18 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 10:40:18 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 10:40:18 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 10:40:18 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 10:40:18 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 10:40:18 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 10:40:22 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 10:40:22 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 10:48:07 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 10:48:07 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 10:48:07 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 10:48:07 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 10:48:07 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 10:48:07 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 10:48:07 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 10:48:07 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 10:48:07 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 10:48:07 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 10:48:07 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 10:48:07 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 10:48:15 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 10:48:15 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 10:48:15 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 10:48:15 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 10:48:15 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 10:48:15 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 10:48:16 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 10:48:16 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 10:48:17 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 10:48:17 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 10:48:17 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 10:48:17 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 10:48:20 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 10:48:20 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 10:49:05 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 10:49:05 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 10:49:05 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 10:49:05 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 10:49:05 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 10:49:05 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 10:49:05 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 10:49:05 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 10:49:05 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 10:49:05 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 10:49:05 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 10:49:05 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 10:49:08 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 10:49:08 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 10:49:08 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 10:49:08 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 10:49:08 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 10:49:08 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 10:49:10 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 10:49:10 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 10:49:10 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 10:49:10 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 10:49:10 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 10:49:10 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 10:49:13 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 10:49:13 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 10:50:30 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 10:50:30 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 10:50:30 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 10:50:30 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 10:50:30 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 10:50:30 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 10:50:30 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 10:50:30 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 10:50:30 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 10:50:30 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 10:50:30 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 10:50:30 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 10:50:34 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 10:50:34 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 10:50:34 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 10:50:34 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 10:50:34 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 10:50:34 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 10:50:36 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 10:50:36 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 10:50:36 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 10:50:36 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 10:50:36 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 10:50:36 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 10:50:40 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 10:50:40 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 10:52:01 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 10:52:01 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 10:52:01 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 10:52:01 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 10:52:01 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 10:52:01 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 10:52:01 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 10:52:01 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 10:52:01 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 10:52:01 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 10:52:01 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 10:52:01 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 10:52:05 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 10:52:05 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 10:52:05 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 10:52:05 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 10:52:05 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 10:52:05 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 10:52:07 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 10:52:07 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 10:52:07 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 10:52:07 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 10:52:07 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 10:52:07 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 10:52:11 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 10:52:11 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 10:52:44 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 10:52:44 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 10:52:44 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 10:52:44 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 10:52:44 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 10:52:44 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 10:52:44 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 10:52:44 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 10:52:44 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 10:52:44 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 10:52:44 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 10:52:44 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 10:53:09 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 10:53:09 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 10:53:09 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 10:53:09 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 10:53:09 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 10:53:09 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 10:53:11 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 10:53:11 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 10:53:11 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 10:53:11 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 10:53:11 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 10:53:11 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 10:53:14 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 10:53:14 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 10:58:17 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 10:58:17 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 10:58:17 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 10:58:17 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 10:58:17 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 10:58:17 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 10:58:17 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 10:58:17 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 10:58:17 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 10:58:17 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 10:58:17 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 10:58:17 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 14:50:02 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 14:50:02 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 14:50:02 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 14:50:02 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 14:50:02 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 14:50:02 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 14:50:03 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 14:50:03 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 14:50:04 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 14:50:04 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 14:50:04 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 14:50:04 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 14:50:08 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 14:50:08 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 15:02:21 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 15:02:21 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 15:02:21 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 15:02:21 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 15:02:21 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 15:02:21 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 15:02:21 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 15:02:21 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 15:02:21 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 15:02:21 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 15:02:21 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 15:02:21 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 15:12:16 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 15:12:16 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 15:12:16 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 15:12:16 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 15:12:16 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 15:12:16 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 15:12:18 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 15:12:18 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 15:12:18 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 15:12:18 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 15:12:18 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 15:12:18 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 15:12:22 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 15:12:22 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 15:16:32 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 15:16:32 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 15:16:32 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 15:16:32 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 15:16:32 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 15:16:32 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 15:16:32 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 15:16:32 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 15:16:32 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 15:16:32 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 15:16:32 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 15:16:32 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 15:16:51 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 15:16:51 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 15:16:51 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 15:16:51 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 15:16:51 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 15:16:51 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 15:16:53 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 15:16:53 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 15:16:53 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 15:16:53 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 15:16:53 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 15:16:53 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 15:16:57 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 15:16:57 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 15:21:03 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 15:21:03 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 15:21:03 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 15:21:03 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 15:21:03 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 15:21:03 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 15:21:03 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 15:21:03 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 15:21:03 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 15:21:03 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 15:21:03 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 15:21:03 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 15:21:06 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 15:21:06 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 15:21:06 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 15:21:06 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 15:21:06 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 15:21:06 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 15:21:08 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 15:21:08 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 15:21:08 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 15:21:08 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 15:21:08 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 15:21:08 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 15:21:12 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 15:21:12 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 15:22:39 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 15:22:39 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 15:22:39 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 15:22:39 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 15:22:39 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 15:22:39 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 15:22:39 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 15:22:39 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 15:22:39 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 15:22:39 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 15:22:39 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 15:22:39 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 15:26:51 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 15:26:51 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 15:26:51 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 15:26:51 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 15:26:51 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 15:26:51 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 15:26:53 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 15:26:53 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 15:26:53 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 15:26:53 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 15:26:53 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 15:26:53 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 15:26:56 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 15:26:56 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 15:28:39 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 15:28:39 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 15:28:39 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 15:28:39 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 15:28:39 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 15:28:39 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 15:28:39 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 15:28:39 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 15:28:39 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 15:28:39 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 15:28:39 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 15:28:39 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 15:41:38 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 15:41:38 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 15:41:38 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 15:41:38 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 15:41:38 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 15:41:38 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 15:41:39 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 15:41:39 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 15:41:39 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 15:41:39 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 15:41:39 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 15:41:39 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 15:41:46 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 15:41:46 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 15:46:21 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 15:46:21 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 15:46:21 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 15:46:21 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 15:46:21 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 15:46:21 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 15:46:21 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 15:46:21 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 15:46:21 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 15:46:21 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 15:46:21 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 15:46:21 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 15:49:27 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 15:49:27 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 15:49:27 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 15:49:27 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 15:49:27 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 15:49:27 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 15:49:29 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 15:49:29 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 15:49:29 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 15:49:29 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 15:49:29 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 15:49:29 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 15:49:36 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 15:49:36 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 15:50:04 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 15:50:04 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 15:50:04 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 15:50:04 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 15:50:04 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 15:50:04 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 15:50:06 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 15:50:06 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 15:50:06 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 15:50:06 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 15:50:06 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 15:50:06 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 15:50:13 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 15:50:13 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 15:53:32 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 15:53:32 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 15:53:32 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 15:53:32 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 15:53:32 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 15:53:32 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 15:53:33 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 15:53:33 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 15:53:34 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 15:53:34 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 15:53:34 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 15:53:34 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 15:53:40 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 15:53:40 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 15:54:38 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 15:54:38 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 15:54:38 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 15:54:38 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 15:54:38 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 15:54:38 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 15:54:39 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 15:54:39 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 15:54:40 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 15:54:40 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 15:54:40 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 15:54:40 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 15:54:47 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 15:54:47 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 16:04:11 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 16:04:11 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 16:04:11 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 16:04:11 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 16:04:11 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 16:04:11 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 16:04:12 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 16:04:12 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 16:04:13 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 16:04:13 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 16:04:13 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 16:04:13 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 16:04:19 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 16:04:19 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 16:04:33 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 16:04:33 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 16:04:33 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 16:04:33 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 16:04:33 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 16:04:33 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 16:04:33 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 16:04:33 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 16:04:33 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 16:04:33 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 16:04:33 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 16:04:33 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 16:04:49 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 16:04:49 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 16:04:49 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 16:04:49 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 16:04:49 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 16:04:49 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 16:04:49 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 16:04:49 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 16:04:49 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 16:04:49 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 16:04:49 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 16:04:49 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 16:06:26 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 16:06:26 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 16:06:26 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 16:06:26 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 16:06:26 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 16:06:26 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 16:06:27 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 16:06:27 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 16:06:28 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 16:06:28 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 16:06:28 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 16:06:28 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 16:06:34 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 16:06:34 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 16:06:54 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 16:06:54 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 16:06:54 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 16:06:54 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 16:06:54 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 16:06:54 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 16:06:54 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 16:06:54 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 16:06:54 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 16:06:54 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 16:06:54 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 16:06:54 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 16:08:01 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 16:08:01 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 16:08:01 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 16:08:01 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 16:08:01 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 16:08:01 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 16:08:03 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 16:08:03 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 16:08:03 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 16:08:03 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 16:08:03 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 16:08:03 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 16:08:10 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 16:08:10 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 16:08:34 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 16:08:34 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 16:08:34 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 16:08:34 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 16:08:34 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 16:08:34 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 16:08:34 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 16:08:34 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 16:08:34 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 16:08:34 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 16:08:34 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 16:08:34 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 16:10:28 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 16:10:28 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 16:10:28 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 16:10:28 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 16:10:28 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 16:10:28 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 16:10:30 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 16:10:30 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 16:10:30 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 16:10:30 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 16:10:30 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 16:10:30 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 16:10:37 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 16:10:37 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 16:10:51 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 16:10:51 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 16:10:51 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 16:10:51 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 16:10:51 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 16:10:51 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 16:10:51 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 16:10:51 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 16:10:51 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 16:10:51 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 16:10:51 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 16:10:51 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 16:12:15 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 16:12:15 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 16:12:15 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 16:12:15 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 16:12:15 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 16:12:15 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 16:12:17 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 16:12:17 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 16:12:17 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 16:12:17 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 16:12:17 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 16:12:17 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 16:12:24 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 16:12:24 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 16:12:40 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 16:12:40 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 16:12:40 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 16:12:40 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 16:12:40 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 16:12:40 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 16:12:42 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 16:12:42 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 16:12:42 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 16:12:42 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 16:12:42 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 16:12:42 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 16:12:49 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 16:12:49 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 16:12:59 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 16:12:59 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 16:12:59 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 16:12:59 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 16:12:59 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 16:12:59 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 16:12:59 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 16:12:59 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 16:13:00 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 16:13:00 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 16:13:00 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 16:13:00 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 16:20:43 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 16:20:43 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 16:20:43 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 16:20:43 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 16:20:43 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 16:20:43 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 16:20:45 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 16:20:45 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 16:20:45 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 16:20:45 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 16:20:45 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 16:20:45 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 16:20:52 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 16:20:52 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 16:20:59 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 16:20:59 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 16:20:59 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 16:20:59 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 16:20:59 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 16:20:59 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 16:20:59 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 16:20:59 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 16:20:59 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 16:20:59 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 16:20:59 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 16:20:59 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 16:22:10 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 16:22:10 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 16:22:10 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 16:22:10 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 16:22:10 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 16:22:10 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 16:22:11 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 16:22:11 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 16:22:11 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 16:22:11 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 16:22:11 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 16:22:11 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 16:22:19 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 16:22:19 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 16:23:37 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 16:23:37 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 16:23:37 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 16:23:37 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 16:23:37 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 16:23:37 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 16:23:37 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 16:23:37 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 16:23:37 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 16:23:37 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 16:23:37 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 16:23:37 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 16:28:04 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 16:28:04 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 16:28:04 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 16:28:04 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 16:28:04 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 16:28:04 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 16:28:05 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 16:28:05 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 16:28:06 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 16:28:06 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 16:28:06 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 16:28:06 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 16:28:13 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 16:28:13 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 16:36:38 - main_controller - [32mINFO[0m - 开始关闭系统...
2025-07-13 16:36:38 - main_controller - [32mINFO[0m - 停止模块: gui
2025-07-13 16:36:38 - main_controller - [32mINFO[0m - 模块 gui 停止成功
2025-07-13 16:36:38 - main_controller - [32mINFO[0m - 停止模块: execution_portfolio
2025-07-13 16:36:38 - main_controller - [32mINFO[0m - 模块 execution_portfolio 停止成功
2025-07-13 16:36:38 - main_controller - [32mINFO[0m - 停止模块: strategy_engine
2025-07-13 16:36:38 - main_controller - [32mINFO[0m - 模块 strategy_engine 停止成功
2025-07-13 16:36:38 - main_controller - [32mINFO[0m - 停止模块: data_service
2025-07-13 16:36:38 - main_controller - [32mINFO[0m - 模块 data_service 停止成功
2025-07-13 16:36:38 - main_controller - [32mINFO[0m - 停止模块: infrastructure
2025-07-13 16:36:38 - main_controller - [32mINFO[0m - 模块 infrastructure 停止成功
2025-07-13 16:36:38 - main_controller - [32mINFO[0m - 系统关闭完成
2025-07-13 16:37:24 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 16:37:24 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 16:37:24 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 16:37:24 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 16:37:24 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 16:37:24 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 16:37:25 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 16:37:25 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 16:37:25 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 16:37:25 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 16:37:25 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 16:37:25 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 16:37:32 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 16:37:32 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 16:42:10 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 16:42:10 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 16:42:10 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 16:42:10 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 16:42:10 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 16:42:10 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 16:42:12 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 16:42:12 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 16:42:12 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 16:42:12 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 16:42:12 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 16:42:12 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 16:42:19 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 16:42:19 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 16:47:27 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 16:47:27 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 16:47:27 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 16:47:27 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 16:47:27 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 16:47:27 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 16:47:29 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 16:47:29 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 16:47:29 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 16:47:29 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 16:47:29 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 16:47:29 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 16:47:30 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 16:47:30 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 16:48:38 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 16:48:38 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 16:48:38 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 16:48:38 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 16:48:38 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 16:48:38 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 16:48:40 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 16:48:40 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 16:48:40 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 16:48:40 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 16:48:40 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 16:48:40 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 16:48:40 - main_controller - [31mERROR[0m - 模块 gui 启动失败: expected 'except' or 'finally' block (main_window.py, line 19)
2025-07-13 16:48:40 - main_controller - [31mERROR[0m - 系统启动失败: expected 'except' or 'finally' block (main_window.py, line 19)
2025-07-13 16:49:22 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 16:49:22 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 16:49:22 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 16:49:22 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 16:49:22 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 16:49:22 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 16:49:23 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 16:49:23 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 16:49:23 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 16:49:23 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 16:49:23 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 16:49:23 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 16:49:24 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 16:49:24 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 16:57:25 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 16:57:25 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 16:57:25 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 16:57:25 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 16:57:25 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 16:57:25 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 16:57:27 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 16:57:27 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 16:57:27 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 16:57:27 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 16:57:27 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 16:57:27 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 16:57:28 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 16:57:28 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 17:00:13 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 17:00:13 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 17:00:13 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 17:00:13 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 17:00:13 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 17:00:13 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 17:00:15 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 17:00:15 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 17:00:15 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 17:00:15 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 17:00:15 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 17:00:15 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 17:00:16 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 17:00:16 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 17:31:19 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 17:31:19 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 17:31:19 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 17:31:19 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 17:31:19 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 17:31:19 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 17:31:21 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 17:31:21 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 17:31:21 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 17:31:21 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 17:31:21 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 17:31:21 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 17:31:22 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 17:31:22 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 17:31:52 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 17:31:52 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 17:31:52 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 17:31:52 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 17:31:52 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 17:31:52 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 17:31:53 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 17:31:53 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 17:31:54 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 17:31:54 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 17:31:54 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 17:31:54 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 17:31:55 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 17:31:55 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-13 17:33:00 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-13 17:33:00 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-13 17:33:00 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-13 17:33:00 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-13 17:33:00 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-13 17:33:00 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-13 17:33:01 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-13 17:33:01 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-13 17:33:02 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-13 17:33:02 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-13 17:33:02 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-13 17:33:02 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-13 17:33:03 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-13 17:33:03 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-14 10:53:51 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-14 10:53:51 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-14 10:53:51 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-14 10:53:51 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-14 10:53:51 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-14 10:53:51 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-14 10:53:53 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-14 10:53:53 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-14 10:53:53 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-14 10:53:53 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-14 10:53:53 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-14 10:53:53 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-14 10:54:00 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-14 10:54:00 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-14 10:59:13 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-14 10:59:13 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-14 10:59:13 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-14 10:59:13 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-14 10:59:13 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-14 10:59:13 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-14 10:59:15 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-14 10:59:15 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-14 10:59:15 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-14 10:59:15 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-14 10:59:15 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-14 10:59:15 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-14 10:59:21 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-14 10:59:21 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-14 11:02:37 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-14 11:02:37 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-14 11:02:37 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-14 11:02:37 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-14 11:02:37 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-14 11:02:37 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-14 11:02:39 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-14 11:02:39 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-14 11:02:39 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-14 11:02:39 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-14 11:02:39 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-14 11:02:39 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-14 11:02:45 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-14 11:02:45 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-14 11:04:43 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-14 11:04:43 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-14 11:04:43 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-14 11:04:43 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-14 11:04:43 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-14 11:04:43 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-14 11:04:45 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-14 11:04:45 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-14 11:04:45 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-14 11:04:45 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-14 11:04:45 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-14 11:04:45 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-14 11:04:51 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-14 11:04:51 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-14 11:17:05 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-14 11:17:05 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-14 11:17:05 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-14 11:17:05 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-14 11:17:05 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-14 11:17:05 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-14 11:17:06 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-14 11:17:06 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-14 11:17:06 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-14 11:17:06 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-14 11:17:06 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-14 11:17:06 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-14 11:17:12 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-14 11:17:12 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-14 11:20:51 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-14 11:20:51 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-14 11:20:51 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-14 11:20:51 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-14 11:20:51 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-14 11:20:51 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-14 11:20:52 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-14 11:20:52 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-14 11:20:52 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-14 11:20:52 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-14 11:20:52 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-14 11:20:52 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-14 11:21:00 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-14 11:21:00 - main_controller - [32mINFO[0m - 量化交易系统启动完成
2025-07-14 11:24:43 - main_controller - [32mINFO[0m - 开始启动量化交易系统...
2025-07-14 11:24:43 - main_controller - [32mINFO[0m - 通知服务初始化完成
2025-07-14 11:24:43 - main_controller - [32mINFO[0m - 事件总线初始化完成
2025-07-14 11:24:43 - main_controller - [32mINFO[0m - 启动模块: infrastructure
2025-07-14 11:24:43 - main_controller - [32mINFO[0m - 模块 infrastructure 启动成功
2025-07-14 11:24:43 - main_controller - [32mINFO[0m - 启动模块: data_service
2025-07-14 11:24:44 - main_controller - [32mINFO[0m - 模块 data_service 启动成功
2025-07-14 11:24:44 - main_controller - [32mINFO[0m - 启动模块: strategy_engine
2025-07-14 11:24:44 - main_controller - [32mINFO[0m - 模块 strategy_engine 启动成功
2025-07-14 11:24:44 - main_controller - [32mINFO[0m - 启动模块: execution_portfolio
2025-07-14 11:24:44 - main_controller - [32mINFO[0m - 模块 execution_portfolio 启动成功
2025-07-14 11:24:44 - main_controller - [32mINFO[0m - 启动模块: gui
2025-07-14 11:24:53 - main_controller - [32mINFO[0m - 模块 gui 启动成功
2025-07-14 11:24:53 - main_controller - [32mINFO[0m - 量化交易系统启动完成
