"""
K线图表视图
使用pyqtgraph实现高性能K线图表
"""

from __future__ import annotations
from typing import Any, Optional, List, Tuple
import logging
from datetime import datetime

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QComboBox, 
    QLabel, QPushButton, QSplitter
)
from PySide6.QtCore import Qt, Signal
import pyqtgraph as pg
import numpy as np

from core.data_types import Interval
from gui.viewmodels.chart_vm import ChartViewModel


class CandlestickItem(pg.GraphicsObject):
    """K线图形项"""
    
    def __init__(self):
        pg.GraphicsObject.__init__(self)
        self.data = None
        self.picture = None
        
    def setData(self, data):
        """设置K线数据"""
        self.data = data
        self.picture = None
        self.informViewBoundsChanged()
        
    def generatePicture(self):
        """生成K线图形"""
        if self.data is None:
            return
            
        self.picture = pg.QtGui.QPicture()
        painter = pg.QtGui.QPainter(self.picture)
        
        # 设置画笔
        pen_up = pg.mkPen(color='#00ff00', width=1)  # 上涨绿色
        pen_down = pg.mkPen(color='#ff0000', width=1)  # 下跌红色
        brush_up = pg.mkBrush('#00ff00')
        brush_down = pg.mkBrush('#ff0000')
        
        # 计算K线宽度（基于时间间隔）
        if len(self.data) > 1:
            time_diff = self.data[1][0] - self.data[0][0]  # 时间戳差值
            w = time_diff * 0.6  # K线宽度为时间间隔的60%
        else:
            w = 60  # 默认1分钟
        
        for timestamp, open_price, high_price, low_price, close_price, volume in self.data:
            # 判断涨跌
            is_up = close_price >= open_price
            
            # 设置颜色
            pen = pen_up if is_up else pen_down
            brush = brush_up if is_up else brush_down
            
            painter.setPen(pen)
            painter.setBrush(brush)
            
            # 绘制影线（使用时间戳作为x坐标）
            painter.drawLine(pg.QtCore.QPointF(timestamp, low_price), pg.QtCore.QPointF(timestamp, high_price))
            
            # 绘制实体
            if is_up:
                # 上涨：空心
                painter.setBrush(pg.mkBrush(None))
                painter.drawRect(pg.QtCore.QRectF(timestamp - w/2, open_price, w, close_price - open_price))
            else:
                # 下跌：实心
                painter.drawRect(pg.QtCore.QRectF(timestamp - w/2, close_price, w, open_price - close_price))
        
        painter.end()
    
    def paint(self, painter, option, widget):
        """绘制"""
        if self.picture is None:
            self.generatePicture()
        if self.picture is not None:
            self.picture.play(painter)
    
    def boundingRect(self):
        """边界矩形"""
        if self.data is None:
            return pg.QtCore.QRectF()
        
        if len(self.data) == 0:
            return pg.QtCore.QRectF()
        
        # 获取时间戳范围
        min_timestamp = min(d[0] for d in self.data)
        max_timestamp = max(d[0] for d in self.data)
        
        # 获取价格范围
        min_price = min(min(d[2], d[3]) for d in self.data)  # min(high, low)
        max_price = max(max(d[2], d[3]) for d in self.data)  # max(high, low)
        
        return pg.QtCore.QRectF(min_timestamp, min_price, max_timestamp - min_timestamp, max_price - min_price)


class ChartView(QWidget):
    """
    K线图表视图
    
    功能:
    1. 显示K线图表
    2. 支持缩放和平移
    3. 显示成交量
    4. 品种和周期切换
    """
    
    # 信号定义
    symbol_changed = Signal(str)
    interval_changed = Signal(str)
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        
        # ViewModel
        self.view_model: Optional[ChartViewModel] = None
        
        # 图表组件
        self.price_plot: Optional[pg.PlotWidget] = None
        self.volume_plot: Optional[pg.PlotWidget] = None
        self.candlestick_item: Optional[CandlestickItem] = None
        self.volume_bars: Optional[pg.BarGraphItem] = None
        
        # 控制组件
        self.symbol_combo: Optional[QComboBox] = None
        self.interval_combo: Optional[QComboBox] = None
        
        # 初始化UI
        self._init_ui()
        
    def _init_ui(self) -> None:
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 控制面板
        control_layout = self._create_control_panel()
        layout.addLayout(control_layout)
        
        # 图表区域
        chart_splitter = self._create_chart_area()
        layout.addWidget(chart_splitter)
        
        # 设置样式
        self._setup_styles()
        
    def _create_control_panel(self) -> QHBoxLayout:
        """创建控制面板"""
        layout = QHBoxLayout()
        
        # 品种选择
        layout.addWidget(QLabel("品种:"))
        self.symbol_combo = QComboBox()
        self.symbol_combo.setEditable(True)
        self.symbol_combo.addItems(['AAPL', 'TSLA', 'MSFT', 'GOOGL'])
        self.symbol_combo.currentTextChanged.connect(self._on_symbol_changed)
        layout.addWidget(self.symbol_combo)
        
        # 周期选择
        layout.addWidget(QLabel("周期:"))
        self.interval_combo = QComboBox()
        intervals = ['1m', '5m', '15m', '30m', '1h', '4h', '1d']
        self.interval_combo.addItems(intervals)
        self.interval_combo.setCurrentText('1m')
        self.interval_combo.currentTextChanged.connect(self._on_interval_changed)
        layout.addWidget(self.interval_combo)
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self._on_refresh_clicked)
        layout.addWidget(refresh_btn)
        
        layout.addStretch()
        return layout
        
    def _create_chart_area(self) -> QSplitter:
        """创建图表区域"""
        splitter = QSplitter(Qt.Vertical)
        
        # 价格图表
        self.price_plot = pg.PlotWidget(title="价格")
        self.price_plot.setLabel('left', '价格')
        self.price_plot.setLabel('bottom', '时间')
        self.price_plot.showGrid(x=True, y=True, alpha=0.3)
        
        # 设置时间轴格式
        time_axis = pg.DateAxisItem(orientation='bottom')
        self.price_plot.setAxisItems({'bottom': time_axis})
        
        # 添加K线图形项
        self.candlestick_item = CandlestickItem()
        self.price_plot.addItem(self.candlestick_item)
        
        splitter.addWidget(self.price_plot)
        
        # 成交量图表
        self.volume_plot = pg.PlotWidget(title="成交量")
        self.volume_plot.setLabel('left', '成交量')
        self.volume_plot.setLabel('bottom', '时间')
        self.volume_plot.showGrid(x=True, y=True, alpha=0.3)
        
        # 设置时间轴格式
        volume_time_axis = pg.DateAxisItem(orientation='bottom')
        self.volume_plot.setAxisItems({'bottom': volume_time_axis})
        
        # 添加成交量柱状图
        self.volume_bars = pg.BarGraphItem(x=[], height=[], width=60, brush='blue')
        self.volume_plot.addItem(self.volume_bars)
        
        splitter.addWidget(self.volume_plot)
        
        # 设置分割比例
        splitter.setSizes([300, 100])
        
        # 链接x轴，使两个图表同步缩放和平移
        self.volume_plot.setXLink(self.price_plot)
        
        return splitter
        
    def _setup_styles(self) -> None:
        """设置样式"""
        # 设置背景色
        pg.setConfigOption('background', 'k')  # 黑色背景
        pg.setConfigOption('foreground', 'w')  # 白色前景
        
    def set_view_model(self, view_model: ChartViewModel) -> None:
        """设置ViewModel"""
        self.view_model = view_model
        
        # 连接信号
        self.view_model.bars_updated.connect(self._on_bars_updated)
        self.view_model.tick_updated.connect(self._on_tick_updated)
        self.view_model.chart_error.connect(self._on_chart_error)
        
        self.logger.info("图表ViewModel已连接")
        
    def _on_symbol_changed(self, symbol: str) -> None:
        """品种变化处理"""
        if symbol and self.view_model:
            interval_str = self.interval_combo.currentText()
            interval = Interval(interval_str)
            self.view_model.set_symbol(symbol, interval)
            self.symbol_changed.emit(symbol)
            
    def _on_interval_changed(self, interval_str: str) -> None:
        """周期变化处理"""
        if interval_str and self.view_model:
            symbol = self.symbol_combo.currentText()
            if symbol:
                interval = Interval(interval_str)
                self.view_model.set_symbol(symbol, interval)
                self.interval_changed.emit(interval_str)
                
    def _on_refresh_clicked(self) -> None:
        """刷新按钮点击"""
        if self.view_model:
            self.view_model.refresh_data()
            
    def _on_bars_updated(self, symbol: str, chart_data: List[Tuple]) -> None:
        """K线数据更新"""
        try:
            if not chart_data:
                return
                
            # 更新K线图
            self.candlestick_item.setData(chart_data)
            
            # 更新成交量图
            x_data = [d[0] for d in chart_data]  # 使用时间戳作为x坐标
            volume_data = [d[5] for d in chart_data]  # volume是第6个元素
            
            # 计算柱状图宽度（基于时间间隔）
            if len(chart_data) > 1:
                time_diff = chart_data[1][0] - chart_data[0][0]  # 时间戳差值
                bar_width = time_diff * 0.6  # 柱状图宽度为时间间隔的60%
            else:
                bar_width = 60  # 默认1分钟
            
            self.volume_bars.setOpts(x=x_data, height=volume_data, width=bar_width)
            
            # 自动调整视图范围
            if len(chart_data) > 0:
                self.price_plot.autoRange()
                self.volume_plot.autoRange()
                
            self.logger.debug(f"更新图表数据: {symbol}, {len(chart_data)}条K线")
            
        except Exception as e:
            self.logger.error(f"更新K线数据失败: {e}")
            
    def _on_tick_updated(self, symbol: str, tick_data: Any) -> None:
        """Tick数据更新"""
        # 可以在这里更新最新价格显示
        pass
        
    def _on_chart_error(self, error_msg: str) -> None:
        """图表错误处理"""
        self.logger.error(f"图表错误: {error_msg}")
        
    def get_current_symbol(self) -> str:
        """获取当前品种"""
        return self.symbol_combo.currentText()
        
    def get_current_interval(self) -> str:
        """获取当前周期"""
        return self.interval_combo.currentText()
        
    def set_symbol(self, symbol: str) -> None:
        """设置品种"""
        self.symbol_combo.setCurrentText(symbol)
