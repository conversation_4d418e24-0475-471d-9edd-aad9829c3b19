"""
K线图表视图
使用pyqtgraph实现高性能K线图表
"""

from __future__ import annotations
from typing import Any, Optional, List, Tuple
import logging
import time
from datetime import datetime

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QComboBox, 
    QLabel, QPushButton, QSplitter
)
from PySide6.QtCore import Qt, Signal
import pyqtgraph as pg
import numpy as np

from core.data_types import Interval
from gui.viewmodels.chart_vm import ChartViewModel
from strategy_ai_engine.indicators.basic_indicators import SMA, EMA, MACD, RSI, BollingerBands


class CandlestickItem(pg.GraphicsObject):
    """K线图形项"""
    
    def __init__(self):
        pg.GraphicsObject.__init__(self)
        self.data = None
        self.picture = None
        
    def setData(self, data):
        """设置K线数据"""
        self.data = data
        self.picture = None
        self.informViewBoundsChanged()
        
    def generatePicture(self):
        """生成K线图形"""
        if self.data is None:
            return
            
        self.picture = pg.QtGui.QPicture()
        painter = pg.QtGui.QPainter(self.picture)
        
        # 设置画笔
        pen_up = pg.mkPen(color='#00ff00', width=1)  # 上涨绿色
        pen_down = pg.mkPen(color='#ff0000', width=1)  # 下跌红色
        brush_up = pg.mkBrush('#00ff00')
        brush_down = pg.mkBrush('#ff0000')
        
        # 计算K线宽度（基于时间间隔）
        if len(self.data) > 1:
            time_diff = self.data[1][0] - self.data[0][0]  # 时间戳差值
            w = time_diff * 0.6  # K线宽度为时间间隔的60%
        else:
            w = 60  # 默认1分钟
        
        for timestamp, open_price, high_price, low_price, close_price, volume in self.data:
            # 判断涨跌
            is_up = close_price >= open_price
            
            # 设置颜色
            pen = pen_up if is_up else pen_down
            brush = brush_up if is_up else brush_down
            
            painter.setPen(pen)
            painter.setBrush(brush)
            
            # 绘制影线（使用时间戳作为x坐标）
            painter.drawLine(pg.QtCore.QPointF(timestamp, low_price), pg.QtCore.QPointF(timestamp, high_price))
            
            # 绘制实体
            if is_up:
                # 上涨：空心
                painter.setBrush(pg.mkBrush(None))
                painter.drawRect(pg.QtCore.QRectF(timestamp - w/2, open_price, w, close_price - open_price))
            else:
                # 下跌：实心
                painter.drawRect(pg.QtCore.QRectF(timestamp - w/2, close_price, w, open_price - close_price))
        
        painter.end()
    
    def paint(self, painter, option, widget):
        """绘制"""
        if self.picture is None:
            self.generatePicture()
        if self.picture is not None:
            self.picture.play(painter)
    
    def boundingRect(self):
        """边界矩形"""
        if self.data is None:
            return pg.QtCore.QRectF()
        
        if len(self.data) == 0:
            return pg.QtCore.QRectF()
        
        # 获取时间戳范围
        min_timestamp = min(d[0] for d in self.data)
        max_timestamp = max(d[0] for d in self.data)
        
        # 获取价格范围
        min_price = min(min(d[2], d[3]) for d in self.data)  # min(high, low)
        max_price = max(max(d[2], d[3]) for d in self.data)  # max(high, low)
        
        return pg.QtCore.QRectF(min_timestamp, min_price, max_timestamp - min_timestamp, max_price - min_price)


class ChartView(QWidget):
    """
    K线图表视图
    
    功能:
    1. 显示K线图表
    2. 支持缩放和平移
    3. 显示成交量
    4. 品种和周期切换
    """
    
    # 信号定义
    symbol_changed = Signal(str)
    interval_changed = Signal(str)
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        
        # ViewModel
        self.view_model: Optional[ChartViewModel] = None
        
        # 图表组件
        self.price_plot: Optional[pg.PlotWidget] = None
        self.volume_plot: Optional[pg.PlotWidget] = None
        self.candlestick_item: Optional[CandlestickItem] = None
        self.volume_bars: Optional[pg.BarGraphItem] = None

        # 控制组件
        self.symbol_combo: Optional[QComboBox] = None
        self.interval_combo: Optional[QComboBox] = None
        self.indicator_combo: Optional[QComboBox] = None

        # 图表控制变量
        self._auto_range_enabled = True
        self._last_auto_range_time = 0
        self._auto_range_interval = 2.0  # 最小自动缩放间隔（秒）
        self._data_changed = False  # 数据是否发生变化
        self._last_data_count = 0  # 上次数据数量

        # 技术指标
        self.indicators = {}  # 存储指标实例
        self.indicator_plots = {}  # 存储指标绘图项
        
        # 初始化UI
        self._init_ui()
        
    def _init_ui(self) -> None:
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 控制面板
        control_layout = self._create_control_panel()
        layout.addLayout(control_layout)
        
        # 图表区域
        chart_splitter = self._create_chart_area()
        layout.addWidget(chart_splitter)
        
        # 设置样式
        self._setup_styles()
        
    def _create_control_panel(self) -> QHBoxLayout:
        """创建控制面板"""
        layout = QHBoxLayout()
        
        # 品种选择
        layout.addWidget(QLabel("品种:"))
        self.symbol_combo = QComboBox()
        self.symbol_combo.setEditable(True)
        self.symbol_combo.addItems(['AAPL', 'TSLA', 'MSFT', 'GOOGL'])
        self.symbol_combo.currentTextChanged.connect(self._on_symbol_changed)
        layout.addWidget(self.symbol_combo)
        
        # 周期选择
        layout.addWidget(QLabel("周期:"))
        self.interval_combo = QComboBox()
        intervals = ['1m', '5m', '15m', '30m', '1h', '4h', '1d']
        self.interval_combo.addItems(intervals)
        self.interval_combo.setCurrentText('1m')
        self.interval_combo.currentTextChanged.connect(self._on_interval_changed)
        layout.addWidget(self.interval_combo)
        
        # 技术指标选择
        layout.addWidget(QLabel("指标:"))
        self.indicator_combo = QComboBox()
        indicators = ['无', 'SMA(5)', 'SMA(10)', 'SMA(20)', 'EMA(12)', 'EMA(26)', 'MACD', 'RSI', '布林带']
        self.indicator_combo.addItems(indicators)
        self.indicator_combo.currentTextChanged.connect(self._on_indicator_changed)
        layout.addWidget(self.indicator_combo)

        # 自动缩放开关
        self.auto_range_btn = QPushButton("自动缩放: 开")
        self.auto_range_btn.setCheckable(True)
        self.auto_range_btn.setChecked(True)
        self.auto_range_btn.clicked.connect(self._on_auto_range_toggled)
        layout.addWidget(self.auto_range_btn)

        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self._on_refresh_clicked)
        layout.addWidget(refresh_btn)

        layout.addStretch()
        return layout
        
    def _create_chart_area(self) -> QSplitter:
        """创建图表区域"""
        splitter = QSplitter(Qt.Vertical)
        
        # 价格图表
        self.price_plot = pg.PlotWidget(title="价格")
        self.price_plot.setLabel('left', '价格')
        self.price_plot.setLabel('bottom', '时间')
        self.price_plot.showGrid(x=True, y=True, alpha=0.3)
        
        # 设置时间轴格式
        time_axis = pg.DateAxisItem(orientation='bottom')
        self.price_plot.setAxisItems({'bottom': time_axis})
        
        # 添加K线图形项
        self.candlestick_item = CandlestickItem()
        self.price_plot.addItem(self.candlestick_item)
        
        splitter.addWidget(self.price_plot)
        
        # 成交量图表
        self.volume_plot = pg.PlotWidget(title="成交量")
        self.volume_plot.setLabel('left', '成交量')
        self.volume_plot.setLabel('bottom', '时间')
        self.volume_plot.showGrid(x=True, y=True, alpha=0.3)
        
        # 设置时间轴格式
        volume_time_axis = pg.DateAxisItem(orientation='bottom')
        self.volume_plot.setAxisItems({'bottom': volume_time_axis})
        
        # 添加成交量柱状图
        self.volume_bars = pg.BarGraphItem(x=[], height=[], width=60, brush='blue')
        self.volume_plot.addItem(self.volume_bars)
        
        splitter.addWidget(self.volume_plot)
        
        # 设置分割比例
        splitter.setSizes([300, 100])
        
        # 链接x轴，使两个图表同步缩放和平移
        self.volume_plot.setXLink(self.price_plot)
        
        return splitter
        
    def _setup_styles(self) -> None:
        """设置样式"""
        # 设置背景色
        pg.setConfigOption('background', 'k')  # 黑色背景
        pg.setConfigOption('foreground', 'w')  # 白色前景
        
    def set_view_model(self, view_model: ChartViewModel) -> None:
        """设置ViewModel"""
        self.view_model = view_model
        
        # 连接信号
        self.view_model.bars_updated.connect(self._on_bars_updated)
        self.view_model.tick_updated.connect(self._on_tick_updated)
        self.view_model.chart_error.connect(self._on_chart_error)
        
        self.logger.info("图表ViewModel已连接")
        
    def _on_symbol_changed(self, symbol: str) -> None:
        """品种变化处理"""
        if symbol and self.view_model:
            interval_str = self.interval_combo.currentText()
            interval = Interval(interval_str)
            self.view_model.set_symbol(symbol, interval)
            self.symbol_changed.emit(symbol)
            
    def _on_interval_changed(self, interval_str: str) -> None:
        """周期变化处理"""
        if interval_str and self.view_model:
            symbol = self.symbol_combo.currentText()
            if symbol:
                interval = Interval(interval_str)
                self.view_model.set_symbol(symbol, interval)
                self.interval_changed.emit(interval_str)
                
    def _on_refresh_clicked(self) -> None:
        """刷新按钮点击"""
        if self.view_model:
            self.view_model.refresh_data()

    def _on_auto_range_toggled(self, checked: bool) -> None:
        """自动缩放开关切换"""
        self._auto_range_enabled = checked
        self.auto_range_btn.setText(f"自动缩放: {'开' if checked else '关'}")
        if not checked:
            # 禁用自动缩放时，允许用户手动缩放
            self.price_plot.enableAutoRange()
            self.volume_plot.enableAutoRange()

    def _on_indicator_changed(self, indicator_str: str) -> None:
        """技术指标变化处理"""
        try:
            # 清除现有指标
            self._clear_indicators()

            if indicator_str == '无':
                return

            # 解析指标类型和参数
            if indicator_str.startswith('SMA'):
                period = int(indicator_str.split('(')[1].split(')')[0])
                self.indicators['SMA'] = SMA(period)
            elif indicator_str.startswith('EMA'):
                period = int(indicator_str.split('(')[1].split(')')[0])
                self.indicators['EMA'] = EMA(period)
            elif indicator_str == 'MACD':
                self.indicators['MACD'] = MACD()
            elif indicator_str == 'RSI':
                self.indicators['RSI'] = RSI()
            elif indicator_str == '布林带':
                self.indicators['BB'] = BollingerBands()

            # 重新计算指标
            self._recalculate_indicators()

        except Exception as e:
            self.logger.error(f"切换技术指标失败: {e}")

    def _clear_indicators(self) -> None:
        """清除所有指标"""
        # 清除指标绘图项
        for plot_items in self.indicator_plots.values():
            if isinstance(plot_items, list):
                for item in plot_items:
                    self.price_plot.removeItem(item)
            else:
                self.price_plot.removeItem(plot_items)

        # 清空缓存
        self.indicators.clear()
        self.indicator_plots.clear()

    def _recalculate_indicators(self) -> None:
        """重新计算指标"""
        # 这个方法会在数据更新时被调用
        pass

    def _update_indicators(self, chart_data: List[Tuple]) -> None:
        """更新技术指标"""
        try:
            if not self.indicators or not chart_data:
                return

            # 清除现有指标绘图
            for plot_items in self.indicator_plots.values():
                if isinstance(plot_items, list):
                    for item in plot_items:
                        self.price_plot.removeItem(item)
                else:
                    self.price_plot.removeItem(plot_items)
            self.indicator_plots.clear()

            # 重置指标
            for indicator in self.indicators.values():
                indicator.reset()

            # 计算指标值
            indicator_data = {}
            for timestamp, open_price, high_price, low_price, close_price, volume in chart_data:
                for name, indicator in self.indicators.items():
                    if name in ['SMA', 'EMA', 'RSI']:
                        value = indicator.update(close_price)
                        if value is not None:
                            if name not in indicator_data:
                                indicator_data[name] = {'x': [], 'y': []}
                            indicator_data[name]['x'].append(timestamp)
                            indicator_data[name]['y'].append(value)
                    elif name == 'MACD':
                        result = indicator.update(close_price)
                        if result is not None:
                            macd_line, signal_line, histogram = result
                            if 'MACD' not in indicator_data:
                                indicator_data['MACD'] = {
                                    'x': [], 'macd': [], 'signal': [], 'histogram': []
                                }
                            indicator_data['MACD']['x'].append(timestamp)
                            indicator_data['MACD']['macd'].append(macd_line)
                            indicator_data['MACD']['signal'].append(signal_line)
                            indicator_data['MACD']['histogram'].append(histogram)
                    elif name == 'BB':
                        result = indicator.update(close_price)
                        if result is not None:
                            upper, middle, lower = result
                            if 'BB' not in indicator_data:
                                indicator_data['BB'] = {
                                    'x': [], 'upper': [], 'middle': [], 'lower': []
                                }
                            indicator_data['BB']['x'].append(timestamp)
                            indicator_data['BB']['upper'].append(upper)
                            indicator_data['BB']['middle'].append(middle)
                            indicator_data['BB']['lower'].append(lower)

            # 绘制指标
            self._plot_indicators(indicator_data)

        except Exception as e:
            self.logger.error(f"更新技术指标失败: {e}")

    def _plot_indicators(self, indicator_data: dict) -> None:
        """绘制技术指标"""
        try:
            for name, data in indicator_data.items():
                if name in ['SMA', 'EMA']:
                    # 绘制移动平均线
                    if len(data['x']) > 0:
                        color = 'yellow' if name == 'SMA' else 'cyan'
                        line = self.price_plot.plot(
                            data['x'], data['y'],
                            pen=pg.mkPen(color=color, width=2),
                            name=name
                        )
                        self.indicator_plots[name] = line

                elif name == 'BB':
                    # 绘制布林带
                    if len(data['x']) > 0:
                        upper_line = self.price_plot.plot(
                            data['x'], data['upper'],
                            pen=pg.mkPen(color='orange', width=1, style=pg.QtCore.Qt.DashLine),
                            name='BB Upper'
                        )
                        middle_line = self.price_plot.plot(
                            data['x'], data['middle'],
                            pen=pg.mkPen(color='blue', width=1),
                            name='BB Middle'
                        )
                        lower_line = self.price_plot.plot(
                            data['x'], data['lower'],
                            pen=pg.mkPen(color='orange', width=1, style=pg.QtCore.Qt.DashLine),
                            name='BB Lower'
                        )
                        self.indicator_plots[name] = [upper_line, middle_line, lower_line]

        except Exception as e:
            self.logger.error(f"绘制技术指标失败: {e}")
            
    def _on_bars_updated(self, symbol: str, chart_data: List[Tuple]) -> None:
        """K线数据更新"""
        try:
            if not chart_data:
                return
                
            # 更新K线图
            self.candlestick_item.setData(chart_data)
            
            # 更新成交量图
            x_data = [d[0] for d in chart_data]  # 使用时间戳作为x坐标
            volume_data = [d[5] for d in chart_data]  # volume是第6个元素
            
            # 计算柱状图宽度（基于时间间隔）
            if len(chart_data) > 1:
                time_diff = chart_data[1][0] - chart_data[0][0]  # 时间戳差值
                bar_width = time_diff * 0.6  # 柱状图宽度为时间间隔的60%
            else:
                bar_width = 60  # 默认1分钟
            
            self.volume_bars.setOpts(x=x_data, height=volume_data, width=bar_width)

            # 检查数据是否发生变化
            current_data_count = len(chart_data)
            if current_data_count != self._last_data_count:
                self._data_changed = True
                self._last_data_count = current_data_count

            # 更新技术指标
            self._update_indicators(chart_data)

            # 只在数据发生变化且满足时间间隔时才自动缩放
            if (len(chart_data) > 0 and self._data_changed and
                self._should_auto_range()):

                # 使用更温和的缩放方式
                if len(chart_data) > 50:  # 只有足够数据时才自动缩放
                    # 只显示最近的数据，避免全局缩放
                    recent_data = chart_data[-50:]  # 最近50条数据
                    if recent_data:
                        min_time = recent_data[0][0]
                        max_time = recent_data[-1][0]
                        min_price = min(min(d[2], d[3]) for d in recent_data)  # min(high, low)
                        max_price = max(max(d[2], d[3]) for d in recent_data)  # max(high, low)

                        # 设置视图范围而不是自动缩放
                        price_range = max_price - min_price
                        padding = price_range * 0.1  # 10%的边距

                        self.price_plot.setXRange(min_time, max_time, padding=0)
                        self.price_plot.setYRange(min_price - padding, max_price + padding, padding=0)
                        self.volume_plot.setXRange(min_time, max_time, padding=0)
                else:
                    # 数据较少时使用自动缩放
                    self.price_plot.autoRange()
                    self.volume_plot.autoRange()

                self._last_auto_range_time = time.time()
                self._data_changed = False

            self.logger.debug(f"更新图表数据: {symbol}, {len(chart_data)}条K线")
            
        except Exception as e:
            self.logger.error(f"更新K线数据失败: {e}")
            
    def _on_tick_updated(self, symbol: str, tick_data: Any) -> None:
        """Tick数据更新"""
        # 可以在这里更新最新价格显示
        pass
        
    def _on_chart_error(self, error_msg: str) -> None:
        """图表错误处理"""
        self.logger.error(f"图表错误: {error_msg}")
        
    def get_current_symbol(self) -> str:
        """获取当前品种"""
        return self.symbol_combo.currentText()
        
    def get_current_interval(self) -> str:
        """获取当前周期"""
        return self.interval_combo.currentText()
        
    def set_symbol(self, symbol: str) -> None:
        """设置品种"""
        self.symbol_combo.setCurrentText(symbol)

    def _should_auto_range(self) -> bool:
        """判断是否应该执行自动缩放"""
        if not self._auto_range_enabled:
            return False

        current_time = time.time()
        # 增加时间间隔，减少自动缩放频率
        return (current_time - self._last_auto_range_time) >= self._auto_range_interval

    def enable_auto_range(self, enabled: bool) -> None:
        """启用/禁用自动缩放"""
        self._auto_range_enabled = enabled

    def set_auto_range_interval(self, interval: float) -> None:
        """设置自动缩放间隔"""
        self._auto_range_interval = max(0.1, interval)  # 最小0.1秒
