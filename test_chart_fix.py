#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图表横坐标修复测试
验证K线图表的时间轴显示是否正确
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import QTimer
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def create_test_data():
    """创建测试数据"""
    # 创建15分钟K线数据
    start_time = datetime.now() - timedelta(days=1)
    timestamps = []
    current_time = start_time
    
    # 生成24小时的15分钟K线数据
    for i in range(96):  # 24小时 * 4个15分钟
        timestamps.append(current_time.timestamp())
        current_time += timedelta(minutes=15)
    
    # 生成价格数据
    base_price = 100.0
    prices = []
    
    for i, ts in enumerate(timestamps):
        # 模拟价格波动
        noise = np.random.normal(0, 1)
        trend = 0.1 * i / len(timestamps)  # 轻微上涨趋势
        price = base_price + trend + noise
        
        open_price = price
        high_price = price + abs(np.random.normal(0, 0.5))
        low_price = price - abs(np.random.normal(0, 0.5))
        close_price = price + np.random.normal(0, 0.3)
        volume = np.random.randint(1000, 10000)
        
        prices.append((ts, open_price, high_price, low_price, close_price, volume))
    
    return prices

def test_chart_display():
    """测试图表显示"""
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = QMainWindow()
    window.setWindowTitle("图表横坐标修复测试")
    window.setGeometry(100, 100, 1200, 800)
    
    central_widget = QWidget()
    window.setCentralWidget(central_widget)
    layout = QVBoxLayout(central_widget)
    
    # 添加说明标签
    info_label = QLabel(
        "图表横坐标修复测试\n"
        "1. 检查K线图的横坐标是否显示正确的时间\n"
        "2. 检查是否可以正常缩放和平移\n"
        "3. 检查成交量图是否与K线图同步\n"
        "4. 检查时间轴格式是否正确"
    )
    layout.addWidget(info_label)
    
    # 创建图表视图
    from gui.views.chart_view import ChartView
    from gui.viewmodels.chart_vm import ChartViewModel
    from core.event_bus import EventBus
    
    # 创建事件总线和配置
    event_bus = EventBus()
    config = {
        'data_service': {
            'storage_path': './data',
            'storage_type': 'feather'
        }
    }
    
    # 创建图表组件
    chart_view = ChartView()
    chart_vm = ChartViewModel(event_bus, config)
    chart_view.set_view_model(chart_vm)
    
    layout.addWidget(chart_view)
    
    # 创建测试按钮
    test_btn = QPushButton("加载测试数据")
    
    def load_test_data():
        """加载测试数据"""
        print("正在加载测试数据...")
        test_data = create_test_data()
        print(f"创建了 {len(test_data)} 条测试数据")
        
        # 直接调用图表更新方法
        chart_view._on_bars_updated("TEST", test_data)
        print("测试数据已加载到图表")
        print("请检查:")
        print("1. 横坐标是否显示正确的时间")
        print("2. 是否可以使用鼠标滚轮缩放")
        print("3. 是否可以拖拽平移")
        print("4. K线图和成交量图是否同步")
    
    test_btn.clicked.connect(load_test_data)
    layout.addWidget(test_btn)
    
    window.show()
    
    print("图表测试窗口已启动")
    print("点击'加载测试数据'按钮来测试图表功能")
    
    return app, window

if __name__ == "__main__":
    app, window = test_chart_display()
    sys.exit(app.exec())